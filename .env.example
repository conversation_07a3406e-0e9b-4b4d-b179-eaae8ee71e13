# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/storywizard

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production

# Azure Storage Configuration - Development & Test
AZURE_STORAGE_ACCOUNT_NAME_DEV=your-storage-account-name-dev
AZURE_STORAGE_ACCOUNT_KEY_DEV=your-storage-account-key-dev
AZURE_STORAGE_CONTAINER_NAME_DEV=storywizard-dev-files

# Azure Storage Configuration - Production
AZURE_STORAGE_ACCOUNT_NAME_PROD=your-storage-account-name-prod
AZURE_STORAGE_ACCOUNT_KEY_PROD=your-storage-account-key-prod
AZURE_STORAGE_CONTAINER_NAME_PROD=storywizard-prod-files

# Stripe Configuration (Optional)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Azure Content Moderation (Optional - uses fallback if not configured)
AZURE_CONTENT_MODERATOR_TEXT_ENDPOINT=https://your-region.api.cognitive.microsoft.com/contentmoderator/moderate/v1.0
AZURE_CONTENT_MODERATOR_IMAGE_ENDPOINT=https://your-region.api.cognitive.microsoft.com/contentmoderator/moderate/v1.0
AZURE_CONTENT_MODERATOR_KEY=your-content-moderator-key