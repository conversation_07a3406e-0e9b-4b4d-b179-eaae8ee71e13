import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Book, Plus, Menu, User, Settings, LogOut, Shield } from "lucide-react";
import { useAuth } from "@/lib/auth";
import { Link, useLocation } from "wouter";
import { BookCard } from "@/components/book-card";
import { SubscriptionBadge } from "@/components/subscription-badge";
import { useState } from "react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { apiRequest } from "@/lib/queryClient";
import { Book as BookType } from "@shared/schema";

export default function Dashboard() {
  const { user, logout } = useAuth();
  const [, navigate] = useLocation();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const { data: books = [], isLoading } = useQuery({
    queryKey: ["/api/books"],
    enabled: !!user,
    queryFn: async (): Promise<BookType[]> => {
      const response = await apiRequest("GET", "/api/books");
      return response.json();
    },
  });

  if (!user) {
    navigate("/auth");
    return null;
  }

  const subscriptionLimits = {
    free: { limit: 1, color: "text-gray-600" },
    basic: { limit: 5, color: "text-primary" },
    pro: { limit: Infinity, color: "text-accent" },
  };

  const currentPlan =
    subscriptionLimits[
      user.subscriptionTier as keyof typeof subscriptionLimits
    ];

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Book className="text-primary text-2xl mr-3" />
          <span className="font-poppins font-bold text-xl">StoryBook AI</span>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2 p-2 rounded-lg bg-gray-100">
            <User className="h-4 w-4" />
            <span className="text-sm font-medium">{user.name}</span>
          </div>
          <SubscriptionBadge tier={user.subscriptionTier} />
        </div>
      </div>

      <nav className="flex-1 px-6">
        <div className="space-y-2">
          <Button variant="ghost" className="w-full justify-start" asChild>
            <Link href="/dashboard">
              <Book className="mr-2 h-4 w-4" />
              My Books
            </Link>
          </Button>
          <Button variant="ghost" className="w-full justify-start" asChild>
            <Link href="/pricing">
              <Settings className="mr-2 h-4 w-4" />
              Subscription
            </Link>
          </Button>
          {(user.role === 'admin' || user.role === 'moderator') && (
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link href="/admin/moderation">
                <Shield className="mr-2 h-4 w-4" />
                Content Moderation
              </Link>
            </Button>
          )}
        </div>
      </nav>

      <div className="p-6 mt-auto">
        <Separator className="mb-4" />
        <Button
          variant="ghost"
          onClick={handleLogout}
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:bg-white lg:border-r lg:border-gray-200">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden fixed top-4 left-4 z-40"
          >
            <Menu className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex-1 lg:ml-64">
        <div className="bg-white border-b border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="font-poppins text-3xl font-bold text-gray-900">
                  Your Books
                </h1>
                <p className="text-gray-600 mt-2">
                  Manage and create your children&apos;s books
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="bg-primary/10 text-primary px-4 py-2 rounded-lg">
                  <span className="text-sm font-medium">
                    {user.subscriptionTier.charAt(0).toUpperCase() +
                      user.subscriptionTier.slice(1)}{" "}
                    Plan • {user.booksUsed}/
                    {currentPlan.limit === Infinity ? "∞" : currentPlan.limit}{" "}
                    books used
                  </span>
                </div>
                <Link href="/book-editor/new">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    New Book
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="h-48 bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-2" />
                    <div className="h-3 bg-gray-200 rounded mb-4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {books.map(book => (
                <BookCard key={book.id} book={book} />
              ))}

              {/* New Book Card */}
              <Card className="border-2 border-dashed border-gray-300 hover:border-primary hover:bg-primary/5 transition-colors cursor-pointer">
                <Link href="/book-editor/new">
                  <CardContent className="p-6 flex flex-col items-center justify-center min-h-80 text-center">
                    <Plus className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
                      Create New Book
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Start with your photos and let AI create a beautiful book
                    </p>
                  </CardContent>
                </Link>
              </Card>
            </div>
          )}

          {books.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <Book className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="font-poppins text-xl font-semibold text-gray-900 mb-2">
                No books yet
              </h3>
              <p className="text-gray-600 mb-6">
                Create your first children&apos;s book to get started
              </p>
              <Link href="/book-editor/new">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Book
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
