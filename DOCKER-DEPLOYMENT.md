# 🐳 Docker Deployment Guide for StoryWizard

## 🚀 Quick Start

### **1. Prerequisites**

- Docker Desktop installed
- Docker Compose available
- Your Azure Storage credentials

### **2. Setup Environment**

```bash
# Copy the Docker environment template
cp .env.docker .env

# Edit .env with your actual values
nano .env
```

**Required values to update in `.env`:**

```env
# Secure database password
DB_PASSWORD=your_secure_database_password

# JWT secret (minimum 32 characters)
JWT_SECRET=your_super_secure_jwt_secret_min_32_chars_long

# Your Azure Storage credentials
AZURE_STORAGE_ACCOUNT_NAME_DEV=your-dev-storage-account
AZURE_STORAGE_ACCOUNT_KEY_DEV=your-dev-storage-key
AZURE_STORAGE_ACCOUNT_NAME_PROD=your-prod-storage-account
AZURE_STORAGE_ACCOUNT_KEY_PROD=your-prod-storage-key
```

### **3. Deploy**

```bash
# Build and start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f app
```

### **4. Access Application**

- **StoryWizard App**: http://localhost:5000
- **Database Admin**: http://localhost:8080 (adminer)
- **Health Check**: http://localhost:5000/api/health

---

## 🏗️ Architecture

### **Services**

- **`postgres`**: PostgreSQL 15 database with persistent storage
- **`app`**: StoryWizard Node.js application
- **`adminer`**: Database administration tool (remove in production)

### **Volumes**

- **`postgres_data`**: Database files (persistent)
- **`app_logs`**: Application logs

### **Networks**

- **`storywizard-network`**: Internal communication between services

---

## 🔧 Configuration Details

### **Environment Variables**

#### **Database (Auto-configured)**

- `DATABASE_URL`: Automatically set to connect to postgres container
- `DB_PASSWORD`: Set in your `.env` file

#### **Application**

- `NODE_ENV`: Set to `production`
- `PORT`: Set to `5000`
- `JWT_SECRET`: Your secure JWT signing key

#### **Azure Storage**

- Development: `AZURE_STORAGE_*_DEV` variables
- Production: `AZURE_STORAGE_*_PROD` variables

#### **Stripe (Optional)**

- `STRIPE_SECRET_KEY`: For payment processing
- `STRIPE_PUBLISHABLE_KEY`: For frontend payments

### **Ports**

- **5000**: StoryWizard application
- **5432**: PostgreSQL database
- **8080**: Adminer database admin

---

## 🛠️ Management Commands

### **Basic Operations**

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart application only
docker-compose restart app

# View real-time logs
docker-compose logs -f app
docker-compose logs -f postgres

# Check service health
docker-compose ps
```

### **Database Operations**

```bash
# Access database directly
docker-compose exec postgres psql -U storywizard -d storywizard

# Run database migrations manually
docker-compose exec app npm run db:push

# Backup database
docker-compose exec postgres pg_dump -U storywizard storywizard > backup.sql

# Restore database
docker-compose exec -T postgres psql -U storywizard storywizard < backup.sql
```

### **Application Operations**

```bash
# Access application container
docker-compose exec app sh

# View application logs
docker-compose exec app tail -f /app/logs/app.log

# Restart with new environment variables
docker-compose up -d --force-recreate app
```

---

## 🔍 Troubleshooting

### **Common Issues**

#### **1. Container Won't Start**

```bash
# Check logs for errors
docker-compose logs app
docker-compose logs postgres

# Check service status
docker-compose ps
```

#### **2. Database Connection Failed**

```bash
# Verify postgres is running
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Test database connectivity
docker-compose exec app pg_isready -h postgres -p 5432 -U storywizard
```

#### **3. Application Health Check Failed**

```bash
# Check health endpoint
curl http://localhost:5000/api/health

# View detailed app logs
docker-compose logs -f app
```

#### **4. File Upload Issues**

- Verify Azure Storage credentials in `.env`
- Check application logs for Azure authentication errors
- Test with small file uploads first

### **Service Dependencies**

- **App depends on**: PostgreSQL (waits for health check)
- **Adminer depends on**: PostgreSQL

### **Port Conflicts**

If ports 5000, 5432, or 8080 are in use:

```yaml
# Edit docker-compose.yml ports section
ports:
  - "5001:5000" # Use different external port
```

---

## 🔒 Security Considerations

### **Production Deployment**

1. **Remove Adminer**: Delete the `adminer` service from docker-compose.yml
2. **Secure Database**: Use strong passwords and consider external database
3. **Environment Variables**: Use Docker secrets or external secret management
4. **Firewall**: Only expose necessary ports (5000 for the app)
5. **HTTPS**: Use reverse proxy (nginx/traefik) for SSL termination

### **Environment Variables Security**

```bash
# Don't commit .env to git
echo ".env" >> .gitignore

# Use secure passwords
openssl rand -base64 32  # Generate secure JWT secret
```

---

## 📊 Monitoring

### **Health Checks**

- **Application**: Built-in health endpoint at `/api/health`
- **Database**: PostgreSQL health check via `pg_isready`
- **Docker**: Health checks configured in docker-compose.yml

### **Logs**

```bash
# Application logs
docker-compose logs -f app

# Database logs
docker-compose logs -f postgres

# All service logs
docker-compose logs -f
```

### **Resource Usage**

```bash
# View resource usage
docker stats

# View disk usage
docker system df
```

---

## 🔄 Updates and Maintenance

### **Update Application**

```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose build app
docker-compose up -d app
```

### **Update Database Schema**

```bash
# Run migrations
docker-compose exec app npm run db:push

# Or restart app (migrations run automatically)
docker-compose restart app
```

### **Backup Strategy**

```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec postgres pg_dump -U storywizard storywizard > "backup_${DATE}.sql"
```

---

## 🎯 Quick Commands Reference

```bash
# Full deployment
cp .env.docker .env && docker-compose up -d

# Check everything is working
curl http://localhost:5000/api/health

# View logs
docker-compose logs -f app

# Access database
docker-compose exec postgres psql -U storywizard -d storywizard

# Clean restart
docker-compose down && docker-compose up -d

# Remove everything (INCLUDING DATA!)
docker-compose down -v
```

---

## ✅ Deployment Checklist

- [ ] Docker and Docker Compose installed
- [ ] `.env` file configured with real values
- [ ] Azure Storage credentials tested
- [ ] Services start successfully: `docker-compose ps`
- [ ] Health check passes: `curl http://localhost:5000/api/health`
- [ ] Application accessible: `http://localhost:5000`
- [ ] Database accessible via Adminer: `http://localhost:8080`
- [ ] File upload works (test with small image)
- [ ] User registration/login works

🎉 **Your StoryWizard application is now running in Docker!**
