import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Save,
  Download,
  Eye,
  ArrowLeft,
  Plus,
  Edit,
  Image,
  Type,
  Shapes,
} from "lucide-react";
import { useAuth } from "@/lib/auth";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { FileUpload } from "@/components/file-upload";
import { PageEditor } from "@/components/page-editor";
import { WithModerationStatus } from "@/components/content-moderation-status";
import { Book, BookPage, InsertBook, InsertBookPage } from "@shared/schema";

export default function BookEditor() {
  const { id } = useParams();
  const [, navigate] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("details");

  const isNewBook = id === "new";
  const bookId = isNewBook ? null : parseInt(id || "");

  const [bookData, setBookData] = useState({
    title: "",
    description: "",
    coverImage: "",
  });

  const { data: book, isLoading: isBookLoading } = useQuery({
    queryKey: [`/api/books/${bookId}`],
    enabled: !!bookId && !!user,
    queryFn: async (): Promise<Book> => {
      const response = await apiRequest("GET", `/api/books/${bookId}`);
      return response.json();
    },
  });

  const { data: pages = [], isLoading: isPagesLoading } = useQuery({
    queryKey: [`/api/books/${bookId}/pages`],
    enabled: !!bookId && !!user,
    queryFn: async (): Promise<BookPage[]> => {
      const response = await apiRequest("GET", `/api/books/${bookId}/pages`);
      return response.json();
    },
  });

  const createBookMutation = useMutation({
    mutationFn: async (data: InsertBook): Promise<Book> => {
      const response = await apiRequest("POST", "/api/books", data);
      return response.json();
    },
    onSuccess: (newBook: Book) => {
      toast({
        title: "Success",
        description: "Book created successfully!",
      });
      navigate(`/book/${newBook.id}`);
      queryClient.invalidateQueries({ queryKey: ["/api/books"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create book",
        variant: "destructive",
      });
    },
  });

  const updateBookMutation = useMutation({
    mutationFn: async (data: Partial<InsertBook>): Promise<Book> => {
      const response = await apiRequest("PUT", `/api/books/${bookId}`, data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Book updated successfully!",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/books/${bookId}`] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update book",
        variant: "destructive",
      });
    },
  });

  const createPageMutation = useMutation({
    mutationFn: async (data: InsertBookPage): Promise<BookPage> => {
      const response = await apiRequest(
        "POST",
        `/api/books/${bookId}/pages`,
        data
      );
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Page added successfully!",
      });
      queryClient.invalidateQueries({
        queryKey: [`/api/books/${bookId}/pages`],
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add page",
        variant: "destructive",
      });
    },
  });

  const exportBookMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("GET", `/api/books/${bookId}/export`);
      return response.blob();
    },
    onSuccess: blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${book?.title || "book"}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast({
        title: "Success",
        description: "Book exported successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to export book",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (book) {
      setBookData({
        title: book.title || "",
        description: book.description || "",
        coverImage: book.coverImage || "",
      });
    }
  }, [book]);

  if (!user) {
    navigate("/auth");
    return null;
  }

  if (!isNewBook && isBookLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  const handleSave = () => {
    if (isNewBook) {
      createBookMutation.mutate(bookData);
    } else {
      updateBookMutation.mutate(bookData);
    }
  };

  const handleAddPage = () => {
    if (!bookId) return;

    const pageNumber = pages.length + 1;
    createPageMutation.mutate({
      bookId,
      pageNumber,
      text: "",
      layout: {},
    });
  };

  const handleExport = () => {
    if (!bookId) return;
    exportBookMutation.mutate();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate("/dashboard")}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="font-poppins text-xl font-semibold text-gray-900 ml-4">
                {isNewBook ? "Create New Book" : book?.title || "Edit Book"}
              </h1>
              {!isNewBook && (
                <Badge variant="secondary" className="ml-2">
                  {book?.status}
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={
                  createBookMutation.isPending || updateBookMutation.isPending
                }
              >
                <Save className="mr-2 h-4 w-4" />
                {createBookMutation.isPending || updateBookMutation.isPending
                  ? "Saving..."
                  : "Save"}
              </Button>
              {!isNewBook && (
                <>
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExport}
                    disabled={exportBookMutation.isPending}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    {exportBookMutation.isPending ? "Exporting..." : "Export"}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <WithModerationStatus
          moderationStatus={book?.moderationStatus}
          moderationMessage={book?.moderationMessage}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">Book Details</TabsTrigger>
            <TabsTrigger value="pages" disabled={isNewBook}>
              Pages
            </TabsTrigger>
            <TabsTrigger value="design" disabled={isNewBook}>
              Design
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Book Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Title</label>
                    <Input
                      value={bookData.title}
                      onChange={e =>
                        setBookData({ ...bookData, title: e.target.value })
                      }
                      placeholder="Enter book title"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cover Image</label>
                    <FileUpload
                      onUpload={file =>
                        setBookData({ ...bookData, coverImage: file.filename })
                      }
                      accept="image/*"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    value={bookData.description}
                    onChange={e =>
                      setBookData({ ...bookData, description: e.target.value })
                    }
                    placeholder="Brief description of your book"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pages" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Pages Sidebar */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Pages
                      <Button
                        size="sm"
                        onClick={handleAddPage}
                        disabled={createPageMutation.isPending}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {isPagesLoading ? (
                        <div className="text-center py-4">
                          <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto" />
                        </div>
                      ) : (
                        pages.map(page => (
                          <div
                            key={page.id}
                            className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">
                                Page {page.pageNumber}
                              </span>
                              <Button size="sm" variant="ghost">
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                            {page.text && (
                              <p className="text-xs text-gray-600 mt-1 truncate">
                                {page.text}
                              </p>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Page Editor */}
              <div className="lg:col-span-3">
                <Card>
                  <CardHeader>
                    <CardTitle>Page Editor</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <PageEditor pages={pages} />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="design" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Design Tools</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <Type className="h-6 w-6 mb-2" />
                    Typography
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Image className="h-6 w-6 mb-2" />
                    Images
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Shapes className="h-6 w-6 mb-2" />
                    Shapes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        </WithModerationStatus>
      </div>
    </div>
  );
}
