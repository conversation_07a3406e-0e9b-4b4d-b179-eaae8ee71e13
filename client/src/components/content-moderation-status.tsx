import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, Clock, AlertTriangle } from "lucide-react";

interface ContentModerationStatusProps {
  status?: string;
  message?: string;
  compact?: boolean;
}

export function ContentModerationStatus({ 
  status = 'pending', 
  message, 
  compact = false 
}: ContentModerationStatusProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          badgeVariant: 'default' as const,
          badgeClass: 'bg-green-100 text-green-800 hover:bg-green-100',
          alertVariant: 'default' as const,
          defaultMessage: 'Content approved and published'
        };
      case 'rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          badgeVariant: 'destructive' as const,
          badgeClass: 'bg-red-100 text-red-800 hover:bg-red-100',
          alertVariant: 'destructive' as const,
          defaultMessage: 'Content rejected - please review and edit'
        };
      case 'flagged':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-600',
          badgeVariant: 'default' as const,
          badgeClass: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
          alertVariant: 'default' as const,
          defaultMessage: 'Content under review - will be available once approved'
        };
      case 'pending':
      default:
        return {
          icon: Clock,
          color: 'text-blue-600',
          badgeVariant: 'secondary' as const,
          badgeClass: 'bg-blue-100 text-blue-800 hover:bg-blue-100',
          alertVariant: 'default' as const,
          defaultMessage: 'Content pending review'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;
  const displayMessage = message || config.defaultMessage;

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <Icon className={`h-4 w-4 ${config.color}`} />
        <Badge className={config.badgeClass}>
          {status}
        </Badge>
      </div>
    );
  }

  // Only show full alert for non-approved content or when there's a specific message
  if (status === 'approved' && !message) {
    return (
      <div className="flex items-center space-x-2 text-sm text-green-600">
        <CheckCircle className="h-4 w-4" />
        <span>Published</span>
      </div>
    );
  }

  return (
    <Alert variant={config.alertVariant} className="mb-4">
      <Icon className="h-4 w-4" />
      <AlertDescription>
        <div className="flex items-center justify-between">
          <span>{displayMessage}</span>
          <Badge className={config.badgeClass}>
            {status}
          </Badge>
        </div>
      </AlertDescription>
    </Alert>
  );
}

// Higher-order component to wrap content with moderation status
interface WithModerationStatusProps {
  moderationStatus?: string;
  moderationMessage?: string;
  showStatusAlways?: boolean;
  children: React.ReactNode;
}

export function WithModerationStatus({ 
  moderationStatus, 
  moderationMessage, 
  showStatusAlways = false,
  children 
}: WithModerationStatusProps) {
  const shouldShowStatus = showStatusAlways || 
    (moderationStatus && moderationStatus !== 'approved');

  return (
    <div>
      {shouldShowStatus && (
        <ContentModerationStatus 
          status={moderationStatus} 
          message={moderationMessage} 
        />
      )}
      {children}
    </div>
  );
}