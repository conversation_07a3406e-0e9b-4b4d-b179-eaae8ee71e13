# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/
out/
server/public

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz
*.tar.gz

# Yarn Integrity file
.yarn-integrity

# IDEs and editors
.idea/
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript
*.tsbuildinfo
vite.config.ts.*

# Vitest
coverage/

# Logs
logs
*.log

# Temporary files
tmp/
temp/
.tmp/

# Database
*.sqlite
*.db
migrations/

# Azure Functions
local.settings.json
bin
obj

# Local uploads (should not exist in cloud storage setup)
uploads/