version: "3.8"

services:
  # PostgreSQL Database (Commented out for simplified setup)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: storywizard-db
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: storywizard
  #     POSTGRES_USER: storywizard
  #     POSTGRES_PASSWORD: ${DB_PASSWORD:-storywizard_secure_password}
  #     PGDATA: /var/lib/postgresql/data/pgdata
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data/pgdata
  #     - ./database/init:/docker-entrypoint-initdb.d
  #   ports:
  #     - "5432:5432"
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U storywizard -d storywizard"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 30s
  #   networks:
  #     - storywizard-network

  # StoryWizard Application (Simplified)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: storywizard-app
    restart: unless-stopped
    environment:
      # Application Configuration
      NODE_ENV: production
      PORT: 3001
    ports:
      - "3001:3001"
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:3001', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - storywizard-network

  # Database Administration (Optional - remove in production)
  adminer:
    image: adminer:4.8.1
    container_name: storywizard-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pappu687
    depends_on:
      - postgres
    networks:
      - storywizard-network

volumes:
  postgres_data:
    driver: local
  app_logs:
    driver: local

networks:
  storywizard-network:
    driver: bridge
