import axios from 'axios';

// Define moderation result types
export interface TextModerationResult {
  isAppropriate: boolean;
  score: number;
  categories: {
    hate: number;
    selfHarm: number;
    sexual: number;
    violence: number;
  };
  flaggedTerms: string[];
  reason?: string;
}

export interface ImageModerationResult {
  isAppropriate: boolean;
  score: number;
  categories: {
    adult: number;
    racy: number;
    violence: number;
    gore: number;
  };
  reason?: string;
}

export class ContentModerationService {
  private readonly textModerationEndpoint: string;
  private readonly imageModerationEndpoint: string;
  private readonly subscriptionKey: string;

  constructor() {
    // Azure Content Moderator endpoints - these will be environment variables
    this.textModerationEndpoint = process.env.AZURE_CONTENT_MODERATOR_TEXT_ENDPOINT || '';
    this.imageModerationEndpoint = process.env.AZURE_CONTENT_MODERATOR_IMAGE_ENDPOINT || '';
    this.subscriptionKey = process.env.AZURE_CONTENT_MODERATOR_KEY || '';
    
    // For development, we'll use a mock service if Azure credentials aren't provided
    if (!this.subscriptionKey) {
      console.warn('Azure Content Moderator credentials not configured - using mock moderation service');
    }
  }

  /**
   * Moderate text content for inappropriate material
   */
  async moderateText(text: string): Promise<TextModerationResult> {
    if (!text || text.trim().length === 0) {
      return {
        isAppropriate: true,
        score: 0,
        categories: { hate: 0, selfHarm: 0, sexual: 0, violence: 0 },
        flaggedTerms: []
      };
    }

    // If Azure credentials aren't configured, use local basic filtering
    if (!this.subscriptionKey) {
      return this.mockTextModeration(text);
    }

    try {
      const response = await axios.post(
        `${this.textModerationEndpoint}/processtext/screen`,
        { text },
        {
          headers: {
            'Ocp-Apim-Subscription-Key': this.subscriptionKey,
            'Content-Type': 'application/json'
          },
          params: {
            language: 'eng',
            autocorrect: true,
            PII: true,
            classify: true
          }
        }
      );

      const data = response.data;
      
      // Parse Azure Content Moderator response
      const categories = {
        hate: data.Classification?.Category1?.Score || 0,
        selfHarm: data.Classification?.Category2?.Score || 0,
        sexual: data.Classification?.Category3?.Score || 0,
        violence: data.PII?.Email?.length > 0 ? 0.3 : 0 // Basic violence detection
      };

      const maxScore = Math.max(...Object.values(categories));
      const flaggedTerms = data.Terms?.map((term: any) => term.Term) || [];
      
      // Content is inappropriate if any category scores above 0.7 or contains flagged terms
      const isAppropriate = maxScore < 0.7 && flaggedTerms.length === 0;
      
      return {
        isAppropriate,
        score: maxScore,
        categories,
        flaggedTerms,
        reason: !isAppropriate ? 'Content flagged for inappropriate language or themes' : undefined
      };

    } catch (error) {
      console.error('Text moderation API error:', error);
      // Fall back to basic moderation on API failure
      return this.mockTextModeration(text);
    }
  }

  /**
   * Moderate image content for inappropriate material
   */
  async moderateImage(imageBuffer: Buffer | string): Promise<ImageModerationResult> {
    // If Azure credentials aren't configured, use mock moderation
    if (!this.subscriptionKey) {
      return this.mockImageModeration();
    }

    try {
      const response = await axios.post(
        `${this.imageModerationEndpoint}/processimage/evaluate`,
        imageBuffer,
        {
          headers: {
            'Ocp-Apim-Subscription-Key': this.subscriptionKey,
            'Content-Type': 'application/octet-stream'
          }
        }
      );

      const data = response.data;
      
      const categories = {
        adult: data.AdultClassificationScore || 0,
        racy: data.RacyClassificationScore || 0,
        violence: 0, // Azure doesn't provide violence score in basic tier
        gore: 0 // Azure doesn't provide gore score in basic tier
      };

      const maxScore = Math.max(categories.adult, categories.racy);
      
      // Content is inappropriate if adult score > 0.8 or racy score > 0.8
      const isAppropriate = categories.adult < 0.8 && categories.racy < 0.8;
      
      return {
        isAppropriate,
        score: maxScore,
        categories,
        reason: !isAppropriate ? 'Image flagged for adult or inappropriate content' : undefined
      };

    } catch (error) {
      console.error('Image moderation API error:', error);
      // Fall back to mock moderation on API failure
      return this.mockImageModeration();
    }
  }

  /**
   * Mock text moderation for development/fallback
   */
  private mockTextModeration(text: string): TextModerationResult {
    const lowerText = text.toLowerCase();
    
    // Basic profanity and inappropriate content detection
    const inappropriatePatterns = [
      /\b(hate|kill|die|stupid|idiot|dumb)\b/g,
      /\b(sex|porn|nude|naked)\b/g,
      /\b(violence|blood|murder|weapon)\b/g,
      /\b(drugs|cocaine|marijuana|weed)\b/g
    ];

    const flaggedTerms: string[] = [];
    let maxScore = 0;
    
    const categories = {
      hate: 0,
      selfHarm: 0,
      sexual: 0,
      violence: 0
    };

    // Check for hate speech
    if (inappropriatePatterns[0].test(lowerText)) {
      categories.hate = 0.8;
      flaggedTerms.push('inappropriate language');
      maxScore = Math.max(maxScore, 0.8);
    }

    // Check for sexual content
    if (inappropriatePatterns[1].test(lowerText)) {
      categories.sexual = 0.9;
      flaggedTerms.push('sexual content');
      maxScore = Math.max(maxScore, 0.9);
    }

    // Check for violence
    if (inappropriatePatterns[2].test(lowerText)) {
      categories.violence = 0.7;
      flaggedTerms.push('violent content');
      maxScore = Math.max(maxScore, 0.7);
    }

    // Check for drug references
    if (inappropriatePatterns[3].test(lowerText)) {
      categories.selfHarm = 0.6;
      flaggedTerms.push('substance references');
      maxScore = Math.max(maxScore, 0.6);
    }

    const isAppropriate = maxScore < 0.7;

    return {
      isAppropriate,
      score: maxScore,
      categories,
      flaggedTerms,
      reason: !isAppropriate ? 'Content flagged by basic content filter' : undefined
    };
  }

  /**
   * Mock image moderation for development/fallback
   */
  private mockImageModeration(): ImageModerationResult {
    // For mock purposes, we'll assume images are appropriate
    // In a real implementation, this might do basic filename checking
    return {
      isAppropriate: true,
      score: 0.1,
      categories: {
        adult: 0.1,
        racy: 0.1,
        violence: 0,
        gore: 0
      }
    };
  }

  /**
   * Check if content should be auto-approved, auto-rejected, or sent for manual review
   */
  getAutoModerationAction(score: number, isAppropriate: boolean): 'auto_approve' | 'auto_reject' | 'manual_review' {
    if (isAppropriate && score < 0.3) {
      return 'auto_approve';
    } else if (!isAppropriate && score > 0.8) {
      return 'auto_reject';
    } else {
      return 'manual_review';
    }
  }

  /**
   * Generate user-friendly moderation message
   */
  getModerationMessage(result: TextModerationResult | ImageModerationResult, contentType: 'text' | 'image'): string {
    if (result.isAppropriate) {
      return 'Content approved';
    }

    const messages = {
      text: 'Your text content has been flagged for review. Please ensure your content follows our community guidelines.',
      image: 'Your image has been flagged for review. Please ensure your images are appropriate for all audiences.'
    };

    return messages[contentType];
  }
}

// Export singleton instance
export const contentModerationService = new ContentModerationService();