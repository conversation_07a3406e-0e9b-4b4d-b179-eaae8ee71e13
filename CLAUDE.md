# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

StoryWizard is a full-stack web application for creating and editing story books. It features:

- User authentication and subscription management (Stripe integration)
- Book creation and editing with page management
- Cloud-based file storage using Azure Blob Storage
- PDF generation capabilities
- Subscription tiers (free, basic, pro)

## Technology Stack

- **Frontend**: React + TypeScript, Vite, Tailwind CSS, Radix UI components
- **Backend**: Express.js + TypeScript, Node.js
- **Database**: PostgreSQL with Drizzle ORM
- **File Storage**: Azure Blob Storage (no local filesystem dependencies)
- **Authentication**: JWT tokens with bcrypt hashing
- **Payment**: Stripe integration
- **Routing**: Wouter for client-side routing

## Common Development Commands

### Development

```bash
# Start development server (runs both frontend and backend)
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### WSL2 Development Environment

**Current WSL2 IP Address**: `************`

**Development Server Access**:

- Start server: `npm run dev`
- Access from Windows browser: `http://************:5000`
- **Important**: Do NOT use `localhost:5000` - it won't work due to WSL2 networking

**Get Current WSL2 IP**:

```bash
hostname -I | awk '{print $1}'
```

**Quick Development Test**:

```bash
# Start server
npm run dev &

# Test access (from WSL2)
curl -I http://$(hostname -I | awk '{print $1}'):5000

# Show Windows browser URL
echo "Access from Windows: http://$(hostname -I | awk '{print $1}'):5000"
```

### Code Quality

```bash
# Run ESLint
npm run lint

# Fix ESLint issues
npm run lint:fix

# Format code with Prettier
npm run format

# Check code formatting
npm run format:check

# TypeScript type checking
npm run check
```

### Testing

```bash
# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Run tests with coverage
npm run test:coverage

# Open Vitest UI
npm run test:ui
```

### Database

```bash
# Push schema changes to database
npm run db:push

# Generate database migrations
npm run db:generate

# Run database migrations
npm run db:migrate
```

### Validation

```bash
# Run all quality checks (format, lint, type check, tests)
npm run validate
```

## Environment Configuration

Required environment variables (see .env.example):

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - JWT signing secret
- **Azure Storage - Development & Test:**
  - `AZURE_STORAGE_ACCOUNT_NAME_DEV` - Azure Storage account name for dev/test
  - `AZURE_STORAGE_ACCOUNT_KEY_DEV` - Azure Storage account key for dev/test
  - `AZURE_STORAGE_CONTAINER_NAME_DEV` - Azure Blob container name (default: storywizard-dev-files)
- **Azure Storage - Production:**
  - `AZURE_STORAGE_ACCOUNT_NAME_PROD` - Azure Storage account name for production
  - `AZURE_STORAGE_ACCOUNT_KEY_PROD` - Azure Storage account key for production
  - `AZURE_STORAGE_CONTAINER_NAME_PROD` - Azure Blob container name (default: storywizard-prod-files)
- `STRIPE_SECRET_KEY` - Stripe secret key (optional)
- `STRIPE_PUBLISHABLE_KEY` - Stripe publishable key (optional)

## Project Structure

- `client/` - React frontend application
  - `src/components/` - React components including UI library (Radix UI)
  - `src/pages/` - Page components
  - `src/lib/` - Utilities (auth, queryClient, stripe, utils)
  - `src/hooks/` - Custom React hooks
- `server/` - Express backend
  - `routes.ts` - API routes and business logic
  - `storage.ts` - Database configuration
  - `azure-storage.ts` - Azure Blob Storage service
  - `vite.ts` - Vite development server setup
- `shared/` - Shared code between frontend and backend
  - `schema.ts` - Drizzle database schema and Zod validation schemas

## Database Schema

Main entities:

- `users` - User accounts with subscription info
- `books` - Story books with metadata
- `bookPages` - Individual pages within books
- `userFiles` - Uploaded files metadata (paths are Azure Blob URLs)

## Cloud Storage Architecture

**Azure Blob Storage Integration:**

- All file uploads are stored in Azure Blob Storage
- No local filesystem dependencies
- **Dual Storage Account Strategy**: Separate storage accounts for better environment isolation
  - **Development & Test**: One shared storage account with prefix separation
    - **Development**: `dev/users/{userId}/{timestamp}-{filename}`
    - **Test**: `test/users/{userId}/{timestamp}-{filename}`
  - **Production**: Separate dedicated storage account
    - **Production**: `users/{userId}/{timestamp}-{filename}`
- Database stores Azure Blob URLs, not local paths
- Supports image formats: JPEG, PNG, GIF, WebP
- 10MB file size limit
- **Environment-specific containers**: Different containers for dev/test vs production

**File Upload Flow:**

1. Client uploads file via `/api/files/upload`
2. Multer processes file in memory
3. File is uploaded to Azure Blob Storage
4. Database stores the Azure Blob URL
5. Client receives the cloud URL

## Core Architecture Patterns

### Authentication & State Management

- **JWT Authentication**: Tokens stored in localStorage with automatic injection via fetch interceptor
- **React Query**: Server state management with automatic caching and invalidation
- **Auth Context**: Global authentication state with user data (client/src/lib/auth.tsx)
- **Protected Routes**: Middleware validates JWT tokens on all API endpoints (server/routes.ts:46)

### Subscription Management

- **Tier-based Limits**: Free (1 book), Basic (5 books), Pro (unlimited)
- **Stripe Integration**: Webhooks update user subscription status in real-time
- **Usage Tracking**: Database tracks books used per user with enforcement

### Storage Architecture

- **Interface-based Design**: IStorage interface supports both memory (dev) and PostgreSQL (prod)
- **Azure Blob Integration**: Dual storage account strategy:
  - **Dev/Test Account**: `dev/users/{userId}/{timestamp}-{filename}` and `test/users/{userId}/{timestamp}-{filename}`
  - **Production Account**: `users/{userId}/{timestamp}-{filename}`
- **Metadata Split**: Database stores file metadata, Azure stores actual files
- **Memory Storage**: MemStorage class for development/testing (server/storage.ts:51)

## Development Environment

### Build Configuration

- **Vite**: Frontend bundling with React plugin and path aliases (`@/` → client/src)
- **ESBuild**: Server compilation for production builds
- **TypeScript**: Strict mode enabled across frontend and backend
- **Tailwind + PostCSS**: CSS processing with custom theme variables

### Development vs Production

- **Development**: Vite middleware with HMR at port 5000
- **Production**: Static file serving from dist/public
- **Conditional Loading**: Replit-specific plugins only in development

### Key Configuration Files

- `drizzle.config.ts` - Database migrations
- `components.json` - ShadCN UI configuration
- `tailwind.config.ts` - Theme customization
- `.env.example` - Required environment variables
- `eslint.config.js` - ESLint configuration
- `.prettierrc` - Prettier formatting rules
- `vitest.config.ts` - Test configuration
- `.husky/` - Git hooks configuration

### Development Tools

- **ESLint**: TypeScript and React linting with automatic fixing
- **Prettier**: Code formatting with pre-commit hooks
- **Vitest**: Fast unit testing with React Testing Library
- **Husky**: Git hooks for code quality enforcement
- **lint-staged**: Run linters on staged files only

## API Architecture

### Route Structure

- `/api/auth/*` - Authentication endpoints (signup, login, me)
- `/api/books/*` - Book CRUD operations
- `/api/books/:id/pages/*` - Nested page management
- `/api/files/*` - File upload and retrieval
- `/api/webhooks/stripe` - Stripe payment webhooks

### Error Handling

- Global error middleware catches unhandled exceptions
- Zod validation with structured error responses (shared/schema.ts)
- JWT verification failures return 403 status
- Authentication middleware at server/routes.ts:46

### Key Implementation Details

- **File Uploads**: Multer with memory storage (server/routes.ts:22)
- **PDF Generation**: PDFKit for book exports (server/routes.ts:446)
- **Stripe Integration**: Conditional loading based on environment (server/routes.ts:40)
- **Database**: Currently using MemStorage for development (server/storage.ts)

## Important Notes

- **Port 5000 Required**: Fixed port for deployment compatibility
- **No Local Storage**: All files must use Azure Blob Storage
- **Memory-based Uploads**: Files processed in memory, never written to disk
- **Shared Schema**: Database types and validation shared between client/server
