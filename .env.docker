# Docker Environment Configuration for StoryWizard
# Copy this to .env and update with your actual values

# Database Configuration (handled by docker-compose)
DB_PASSWORD=storywizard

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_change_in_production_min_32_chars

# Azure Storage Configuration - Development & Test
# Replace with your actual Azure Storage credentials for dev/test
AZURE_STORAGE_ACCOUNT_NAME_DEV=your-dev-storage-account
AZURE_STORAGE_ACCOUNT_KEY_DEV=your-dev-storage-key
AZURE_STORAGE_CONTAINER_NAME_DEV=storywizard-dev-files

# Azure Storage Configuration - Production
# Replace with your actual Azure Storage credentials for production
AZURE_STORAGE_ACCOUNT_NAME_PROD=your-prod-storage-account
AZURE_STORAGE_ACCOUNT_KEY_PROD=your-prod-storage-key
AZURE_STORAGE_CONTAINER_NAME_PROD=storywizard-prod-files

# Stripe Configuration (Optional)
# Leave empty if not using Stripe payments
STRIPE_SECRET_KEY=
STRIPE_PUBLISHABLE_KEY=

# Note: The following are automatically set by docker-compose:
# - DATABASE_URL (points to postgres container)
# - NODE_ENV=production
# - PORT=5000