import type { Express } from "express";
import { createServer, type Server } from "http";
import * as bcrypt from "bcrypt";
import * as jwt from "jsonwebtoken";
import multer from "multer";
import PDFDocument from "pdfkit";
import Stripe from "stripe";
import { storage } from "./storage";
import { AzureStorageService } from "./azure-storage";
import { moderationService } from "./moderation-service";
import {
  signupSchema,
  loginSchema,
  insertBookSchema,
  insertBookPageSchema,
} from "../shared/schema";
import { z } from "zod";

const JWT_SECRET =
  process.env.JWT_SECRET || "your-secret-key-change-in-production";

// Configure multer for file uploads (memory storage for Azure Blob)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
        )
      );
    }
  },
});

// Initialize Stripe if keys are available
let stripe: Stripe | null = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
}

// Auth middleware
const authenticateToken = (req: any, res: any, next: any) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    return res.sendStatus(401);
  }

  jwt.verify(token, JWT_SECRET, async (err: any, decoded: any) => {
    if (err) return res.sendStatus(403);

    const user = await storage.getUser(decoded.userId);
    if (!user) return res.sendStatus(403);

    req.user = user;
    next();
  });
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Health check endpoint
  app.get("/api/health", async (req: any, res: any) => {
    try {
      // Check database connectivity
      await storage.getHealthCheck();
      res.status(200).json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        services: {
          database: "connected",
          azure_storage: "configured",
        },
      });
    } catch (error) {
      console.error("Health check failed:", error);
      res.status(503).json({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Database connection failed",
      });
    }
  });

  // Auth routes
  app.post("/api/auth/signup", async (req: any, res: any) => {
    try {
      const validatedData = signupSchema.parse(req.body);

      // Check if user already exists
      const existingUser = await storage.getUserByEmail(validatedData.email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      const user = await storage.createUser(validatedData);
      const token = jwt.sign({ userId: user.id }, JWT_SECRET, {
        expiresIn: "7d",
      });

      const { password: _, ...userWithoutPassword } = user;
      res.json({ user: userWithoutPassword, token });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid input", errors: error.errors });
      }
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/auth/login", async (req: any, res: any) => {
    try {
      const validatedData = loginSchema.parse(req.body);

      const user = await storage.getUserByEmail(validatedData.email);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      const isValidPassword = await bcrypt.compare(
        validatedData.password,
        user.password
      );
      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      const token = jwt.sign({ userId: user.id }, JWT_SECRET, {
        expiresIn: "7d",
      });

      const { password: _, ...userWithoutPassword } = user;
      res.json({ user: userWithoutPassword, token });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid input", errors: error.errors });
      }
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/auth/me", authenticateToken, async (req: any, res: any) => {
    const { password: _, ...userWithoutPassword } = req.user;
    res.json({ user: userWithoutPassword });
  });

  // Book routes
  app.get("/api/books", authenticateToken, async (req: any, res: any) => {
    try {
      const books = await storage.getBooksByUserId(req.user.id);
      res.json(books);
    } catch (error) {
      console.error("Error fetching books:", error);
      res.status(500).json({ message: "Error fetching books" });
    }
  });

  app.post("/api/books", authenticateToken, async (req: any, res: any) => {
    try {
      const validatedData = insertBookSchema.parse(req.body);

      // Check subscription limits
      const user = req.user;
      const limits = { free: 1, basic: 5, pro: Infinity };
      const userLimit =
        limits[user.subscriptionTier as keyof typeof limits] || 0;

      if (user.booksUsed >= userLimit) {
        return res.status(403).json({
          message: "Book limit reached for your subscription tier",
          limit: userLimit,
          used: user.booksUsed,
        });
      }

      const book = await storage.createBook({
        ...validatedData,
        userId: user.id,
      });

      // Moderate book text content (title + description)
      const textToModerate = `${validatedData.title} ${validatedData.description || ''}`.trim();
      
      if (textToModerate) {
        const moderationResult = await moderationService.moderateTextContent(
          'book',
          book.id,
          user.id,
          textToModerate
        );

        // If content is rejected, delete the book and don't count it
        if (moderationResult.status === 'rejected') {
          await storage.deleteBook(book.id);
          return res.status(400).json({
            message: 'Book content violates community guidelines',
            details: moderationResult.result.reason,
            moderationStatus: 'rejected'
          });
        }
      }

      // Update user's books used count only if book is approved or flagged for review
      await storage.updateUser(user.id, { booksUsed: user.booksUsed + 1 });

      res.json({
        ...book,
        moderationStatus: book.moderationStatus || 'pending',
        moderationMessage: book.moderationStatus === 'approved'
          ? 'Book approved'
          : book.moderationStatus === 'flagged'
          ? 'Book under review'
          : 'Book pending review'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid input", errors: error.errors });
      }
      console.error('Book creation error:', error);
      res.status(500).json({ message: "Error creating book" });
    }
  });

  app.get("/api/books/:id", authenticateToken, async (req: any, res: any) => {
    try {
      const bookId = parseInt(req.params.id);
      if (isNaN(bookId)) {
        return res.status(400).json({ message: "Invalid book ID" });
      }

      const book = await storage.getBook(bookId);

      if (!book || book.userId !== req.user.id) {
        return res.status(404).json({ message: "Book not found" });
      }

      res.json(book);
    } catch (error) {
      console.error("Error fetching book:", error);
      res.status(500).json({ message: "Error fetching book" });
    }
  });

  app.put("/api/books/:id", authenticateToken, async (req: any, res: any) => {
    try {
      const bookId = parseInt(req.params.id);
      if (isNaN(bookId)) {
        return res.status(400).json({ message: "Invalid book ID" });
      }

      const book = await storage.getBook(bookId);

      if (!book || book.userId !== req.user.id) {
        return res.status(404).json({ message: "Book not found" });
      }

      const validatedData = insertBookSchema.partial().parse(req.body);
      const updatedBook = await storage.updateBook(bookId, validatedData);

      res.json(updatedBook);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid input", errors: error.errors });
      }
      res.status(500).json({ message: "Error updating book" });
    }
  });

  app.delete(
    "/api/books/:id",
    authenticateToken,
    async (req: any, res: any) => {
      try {
        const bookId = parseInt(req.params.id);
        if (isNaN(bookId)) {
          return res.status(400).json({ message: "Invalid book ID" });
        }

        const book = await storage.getBook(bookId);

        if (!book || book.userId !== req.user.id) {
          return res.status(404).json({ message: "Book not found" });
        }

        await storage.deleteBook(bookId);

        // Update user's books used count
        const user = req.user;
        await storage.updateUser(user.id, {
          booksUsed: Math.max(0, user.booksUsed - 1),
        });

        res.json({ message: "Book deleted successfully" });
      } catch (error) {
        console.error("Error deleting book:", error);
        res.status(500).json({ message: "Error deleting book" });
      }
    }
  );

  // Book pages routes
  app.get(
    "/api/books/:bookId/pages",
    authenticateToken,
    async (req: any, res: any) => {
      try {
        const bookId = parseInt(req.params.bookId);
        if (isNaN(bookId)) {
          return res.status(400).json({ message: "Invalid book ID" });
        }

        const book = await storage.getBook(bookId);

        if (!book || book.userId !== req.user.id) {
          return res.status(404).json({ message: "Book not found" });
        }

        const pages = await storage.getBookPages(bookId);
        res.json(pages);
      } catch (error) {
        console.error("Error fetching pages:", error);
        res.status(500).json({ message: "Error fetching pages" });
      }
    }
  );

  app.post(
    "/api/books/:bookId/pages",
    authenticateToken,
    async (req: any, res: any) => {
      try {
        const bookId = parseInt(req.params.bookId);
        if (isNaN(bookId)) {
          return res.status(400).json({ message: "Invalid book ID" });
        }

        const book = await storage.getBook(bookId);

        if (!book || book.userId !== req.user.id) {
          return res.status(404).json({ message: "Book not found" });
        }

        const validatedData = insertBookPageSchema.parse({
          ...req.body,
          bookId,
        });

        const page = await storage.createBookPage({
          ...validatedData,
          userId: req.user.id,
        });

        // Moderate page text content if provided
        if (validatedData.text && validatedData.text.trim()) {
          const moderationResult = await moderationService.moderateTextContent(
            'page',
            page.id,
            req.user.id,
            validatedData.text
          );

          // If content is rejected, delete the page
          if (moderationResult.status === 'rejected') {
            await storage.deleteBookPage(page.id);
            return res.status(400).json({
              message: 'Page content violates community guidelines',
              details: moderationResult.result.reason,
              moderationStatus: 'rejected'
            });
          }
        }

        res.json({
          ...page,
          moderationStatus: page.moderationStatus || 'pending',
          moderationMessage: page.moderationStatus === 'approved'
            ? 'Page approved'
            : page.moderationStatus === 'flagged'
            ? 'Page under review'
            : 'Page pending review'
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res
            .status(400)
            .json({ message: "Invalid input", errors: error.errors });
        }
        console.error('Page creation error:', error);
        res.status(500).json({ message: "Error creating page" });
      }
    }
  );

  app.put(
    "/api/books/:bookId/pages/:pageId",
    authenticateToken,
    async (req: any, res: any) => {
      try {
        const bookId = parseInt(req.params.bookId);
        const pageId = parseInt(req.params.pageId);

        if (isNaN(bookId) || isNaN(pageId)) {
          return res.status(400).json({ message: "Invalid book or page ID" });
        }

        const book = await storage.getBook(bookId);
        if (!book || book.userId !== req.user.id) {
          return res.status(404).json({ message: "Book not found" });
        }

        const validatedData = insertBookPageSchema.partial().parse(req.body);

        // If text content is being updated, moderate it
        if (validatedData.text && validatedData.text.trim()) {
          const moderationResult = await moderationService.moderateTextContent(
            'page',
            pageId,
            req.user.id,
            validatedData.text
          );

          // If content is rejected, don't update the page
          if (moderationResult.status === 'rejected') {
            return res.status(400).json({
              message: 'Page content violates community guidelines',
              details: moderationResult.result.reason,
              moderationStatus: 'rejected'
            });
          }
        }

        const updatedPage = await storage.updateBookPage(pageId, validatedData);

        if (!updatedPage) {
          return res.status(404).json({ message: "Page not found" });
        }

        res.json({
          ...updatedPage,
          moderationStatus: updatedPage.moderationStatus || 'pending',
          moderationMessage: updatedPage.moderationStatus === 'approved'
            ? 'Page approved'
            : updatedPage.moderationStatus === 'flagged'
            ? 'Page under review'
            : 'Page pending review'
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res
            .status(400)
            .json({ message: "Invalid input", errors: error.errors });
        }
        console.error('Page update error:', error);
        res.status(500).json({ message: "Error updating page" });
      }
    }
  );

  // File upload routes
  app.post(
    "/api/files/upload",
    authenticateToken,
    upload.single("file"),
    async (req: any, res: any) => {
      try {
        if (!req.file) {
          return res.status(400).json({ message: "No file uploaded" });
        }

        // Upload to Azure Blob Storage first
        const uploadResult = await AzureStorageService.uploadFile(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype,
          req.user.id
        );

        // Create file record in database
        const userFile = await storage.createUserFile({
          userId: req.user.id,
          filename: uploadResult.filename,
          originalName: req.file.originalname,
          mimetype: req.file.mimetype,
          size: uploadResult.size,
          path: uploadResult.url,
        });

        // Moderate image content
        const moderationResult = await moderationService.moderateImageContent(
          'file',
          userFile.id,
          req.user.id,
          req.file.buffer
        );

        // Return file info with moderation status
        res.json({
          ...userFile,
          url: uploadResult.url,
          moderationStatus: moderationResult.status,
          moderationMessage: moderationResult.status === 'approved' 
            ? 'Image approved' 
            : moderationResult.status === 'rejected' 
            ? 'Image rejected - please choose a different image'
            : 'Image under review - it will be available once approved'
        });
      } catch (error) {
        console.error("File upload error:", error);
        res.status(500).json({ message: "Error uploading file" });
      }
    }
  );

  app.get("/api/files/:id", authenticateToken, async (req: any, res: any) => {
    try {
      const fileId = parseInt(req.params.id);
      if (isNaN(fileId)) {
        return res.status(400).json({ message: "Invalid file ID" });
      }

      const file = await storage.getUserFile(fileId);

      if (!file || file.userId !== req.user.id) {
        return res.status(404).json({ message: "File not found" });
      }

      // Return the cloud URL directly
      res.json({
        ...file,
        url: file.path, // path now contains the Azure Blob URL
      });
    } catch (error) {
      console.error("Error serving file:", error);
      res.status(500).json({ message: "Error serving file" });
    }
  });

  // PDF export route
  app.get(
    "/api/books/:id/export",
    authenticateToken,
    async (req: any, res: any) => {
      try {
        const bookId = parseInt(req.params.id);
        if (isNaN(bookId)) {
          return res.status(400).json({ message: "Invalid book ID" });
        }

        const book = await storage.getBook(bookId);

        if (!book || book.userId !== req.user.id) {
          return res.status(404).json({ message: "Book not found" });
        }

        const pages = await storage.getBookPages(bookId);

        // Create PDF
        const doc = new (PDFDocument as any)({ size: "A4", margin: 50 });

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${book.title.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.pdf"`
        );

        doc.pipe(res);

        // Add title page
        doc.fontSize(24).text(book.title, { align: "center" });
        if (book.description) {
          doc.moveDown();
          doc.fontSize(14).text(book.description, { align: "center" });
        }

        // Add watermark for free tier
        if (req.user.subscriptionTier === "free") {
          doc
            .fontSize(10)
            .fillColor("gray")
            .text("Created with StoryBook AI - Free Tier", 50, 750);
        }

        // Add pages
        for (const page of pages) {
          doc.addPage();

          if (page.imageUrl) {
            try {
              // In a real implementation, you'd process the image here
              doc.fontSize(12).text(`[Image: ${page.imageUrl}]`, 50, 100);
            } catch (error) {
              console.error("Error processing image:", error);
            }
          }

          if (page.text) {
            doc
              .fontSize(14)
              .fillColor("black")
              .text(page.text, 50, 200, { width: 500 });
          }

          doc.fontSize(10).text(`Page ${page.pageNumber}`, 50, 750);
        }

        doc.end();
      } catch (error) {
        console.error("PDF export error:", error);
        res.status(500).json({ message: "Error generating PDF" });
      }
    }
  );

  // Stripe payment routes
  if (stripe) {
    app.post(
      "/api/create-subscription",
      authenticateToken,
      async (req: any, res: any) => {
        try {
          const { priceId } = req.body;

          if (!priceId) {
            return res.status(400).json({ message: "Price ID is required" });
          }

          let user = req.user;

          // Create customer if doesn't exist
          if (!user.stripeCustomerId) {
            const customer = await stripe.customers.create({
              email: user.email,
              name: user.name,
            });

            user =
              (await storage.updateUser(user.id, {
                stripeCustomerId: customer.id,
              })) || user;
          }

          // Create subscription
          const subscription = await stripe.subscriptions.create({
            customer: user.stripeCustomerId!,
            items: [{ price: priceId }],
            payment_behavior: "default_incomplete",
            payment_settings: {
              save_default_payment_method: "on_subscription",
            },
            expand: ["latest_invoice.payment_intent"],
          });

          res.json({
            subscriptionId: subscription.id,
            clientSecret: (subscription.latest_invoice as any)?.payment_intent
              ?.client_secret,
          });
        } catch (error: any) {
          console.error("Stripe subscription error:", error);
          res
            .status(500)
            .json({ message: "Error creating subscription: " + error.message });
        }
      }
    );

    app.post("/api/webhooks/stripe", async (req: any, res: any) => {
      const sig = req.headers["stripe-signature"];
      let event;

      try {
        if (!process.env.STRIPE_WEBHOOK_SECRET) {
          throw new Error("Stripe webhook secret not configured");
        }

        event = stripe.webhooks.constructEvent(
          req.body,
          sig!,
          process.env.STRIPE_WEBHOOK_SECRET
        );
      } catch (err: any) {
        console.error("Webhook signature verification failed:", err.message);
        return res
          .status(400)
          .send(`Webhook signature verification failed: ${err.message}`);
      }

      try {
        // Handle subscription events
        if (
          event.type === "customer.subscription.updated" ||
          event.type === "customer.subscription.created"
        ) {
          const subscription = event.data.object;
          const customer = await stripe.customers.retrieve(
            subscription.customer as string
          );

          if (customer && !customer.deleted) {
            const user = await storage.getUserByEmail(customer.email!);
            if (user) {
              let tier = "free";
              if (subscription.status === "active") {
                // Map price ID to tier - you'll need to set these in environment
                const priceToTier: { [key: string]: string } = {
                  [process.env.STRIPE_BASIC_PRICE_ID || "price_basic"]: "basic",
                  [process.env.STRIPE_PRO_PRICE_ID || "price_pro"]: "pro",
                };
                tier =
                  priceToTier[subscription.items.data[0].price.id] || "free";
              }

              await storage.updateUser(user.id, {
                subscriptionTier: tier,
                stripeSubscriptionId: subscription.id,
              });
            }
          }
        }

        res.json({ received: true });
      } catch (error) {
        console.error("Webhook processing error:", error);
        res.status(500).json({ message: "Webhook processing failed" });
      }
    });
  } else {
    console.warn("Stripe not configured - payment features disabled");
  }

  // Admin middleware for content moderation
  const requireAdmin = (req: any, res: any, next: any) => {
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
      return res.status(403).json({ message: 'Admin access required' });
    }
    next();
  };

  // Admin content moderation routes
  app.get('/api/admin/moderation/queue', authenticateToken, requireAdmin, async (req: any, res: any) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const contentForReview = await moderationService.getContentForReview(limit);
      res.json(contentForReview);
    } catch (error) {
      console.error('Error fetching moderation queue:', error);
      res.status(500).json({ message: 'Error fetching moderation queue' });
    }
  });

  app.post('/api/admin/moderation/:contentType/:contentId/approve', 
    authenticateToken, requireAdmin, async (req: any, res: any) => {
    try {
      const { contentType, contentId } = req.params;
      const { notes } = req.body;
      
      await moderationService.approveContent(
        contentType as any, 
        parseInt(contentId), 
        req.user.id, 
        notes
      );
      
      res.json({ message: 'Content approved successfully' });
    } catch (error) {
      console.error('Error approving content:', error);
      res.status(500).json({ message: 'Error approving content' });
    }
  });

  app.post('/api/admin/moderation/:contentType/:contentId/reject', 
    authenticateToken, requireAdmin, async (req: any, res: any) => {
    try {
      const { contentType, contentId } = req.params;
      const { reason, notes } = req.body;
      
      if (!reason) {
        return res.status(400).json({ message: 'Rejection reason is required' });
      }
      
      await moderationService.rejectContent(
        contentType as any, 
        parseInt(contentId), 
        req.user.id, 
        reason,
        notes
      );
      
      res.json({ message: 'Content rejected successfully' });
    } catch (error) {
      console.error('Error rejecting content:', error);
      res.status(500).json({ message: 'Error rejecting content' });
    }
  });

  app.get('/api/admin/moderation/stats', authenticateToken, requireAdmin, async (req: any, res: any) => {
    try {
      const stats = await moderationService.getModerationStats();
      res.json(stats);
    } catch (error) {
      console.error('Error fetching moderation stats:', error);
      res.status(500).json({ message: 'Error fetching moderation stats' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
