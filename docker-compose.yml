version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: storywizard-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: storywizard
      POSTGRES_USER: storywizard
      POSTGRES_PASSWORD: ${DB_PASSWORD:-storywizard_secure_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U storywizard -d storywizard"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - storywizard-network

  # StoryWizard Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: storywizard-app
    restart: unless-stopped
    environment:
      # Database Configuration
      DATABASE_URL: postgresql://storywizard:${DB_PASSWORD:-storywizard_secure_password}@postgres:5432/storywizard

      # Application Configuration
      NODE_ENV: production
      PORT: 5000
      JWT_SECRET: ${JWT_SECRET:-your_super_secure_jwt_secret_change_in_production}

      # Azure Storage Configuration - Development & Test
      AZURE_STORAGE_ACCOUNT_NAME_DEV: ${AZURE_STORAGE_ACCOUNT_NAME_DEV}
      AZURE_STORAGE_ACCOUNT_KEY_DEV: ${AZURE_STORAGE_ACCOUNT_KEY_DEV}
      AZURE_STORAGE_CONTAINER_NAME_DEV: ${AZURE_STORAGE_CONTAINER_NAME_DEV:-storywizard-dev-files}

      # Azure Storage Configuration - Production
      AZURE_STORAGE_ACCOUNT_NAME_PROD: ${AZURE_STORAGE_ACCOUNT_NAME_PROD}
      AZURE_STORAGE_ACCOUNT_KEY_PROD: ${AZURE_STORAGE_ACCOUNT_KEY_PROD}
      AZURE_STORAGE_CONTAINER_NAME_PROD: ${AZURE_STORAGE_CONTAINER_NAME_PROD:-storywizard-prod-files}

      # Stripe Configuration (Optional)
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
    ports:
      - "5000:5000"
    volumes:
      - app_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - storywizard-network

  # Database Administration (Optional - remove in production)
  adminer:
    image: adminer:4.8.1
    container_name: storywizard-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pappu687
    depends_on:
      - postgres
    networks:
      - storywizard-network

volumes:
  postgres_data:
    driver: local
  app_logs:
    driver: local

networks:
  storywizard-network:
    driver: bridge
