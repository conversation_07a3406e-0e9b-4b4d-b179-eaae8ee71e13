#!/bin/bash

# Docker startup script for StoryWizard
# This script runs database migrations and starts the application

set -e

echo "🚀 Starting StoryWizard Docker container..."

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
while ! pg_isready -h postgres -p 5432 -U storywizard; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Run database migrations
echo "📊 Running database migrations..."
npx drizzle-kit push --force

echo "🌟 Starting StoryWizard application..."
exec npm start