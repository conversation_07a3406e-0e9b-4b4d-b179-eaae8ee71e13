import { Badge } from "@/components/ui/badge";
import { Crown, Star, Sparkles } from "lucide-react";

interface SubscriptionBadgeProps {
  tier: string;
  showIcon?: boolean;
}

export function SubscriptionBadge({
  tier,
  showIcon = true,
}: SubscriptionBadgeProps) {
  const getConfig = (tier: string) => {
    switch (tier) {
      case "free":
        return {
          label: "Free",
          variant: "secondary" as const,
          icon: Sparkles,
          className: "bg-gray-100 text-gray-700",
        };
      case "basic":
        return {
          label: "Basic",
          variant: "default" as const,
          icon: Star,
          className: "bg-primary/10 text-primary",
        };
      case "pro":
        return {
          label: "Pro",
          variant: "default" as const,
          icon: Crown,
          className: "bg-accent/10 text-accent",
        };
      default:
        return {
          label: "Free",
          variant: "secondary" as const,
          icon: Sparkles,
          className: "bg-gray-100 text-gray-700",
        };
    }
  };

  const config = getConfig(tier);
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className={config.className}>
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      {config.label}
    </Badge>
  );
}
