import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Edit,
  Type,
  Image,
  Shapes,
  Move,
  Trash2,
  Plus,
  Eye,
  Download,
  Palette,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
} from "lucide-react";
import { BookPage } from "@shared/schema";

interface PageEditorProps {
  pages: BookPage[];
}

export function PageEditor({ pages }: PageEditorProps) {
  const [selectedPage] = useState<BookPage | null>(pages[0] || null);
  const [editingText, setEditingText] = useState(false);
  const [textContent, setTextContent] = useState(selectedPage?.text || "");
  const [selectedElement, setSelectedElement] = useState<
    "image" | "text" | null
  >(null);

  // const handlePageSelect = (page: BookPage) => {
  //   setSelectedPage(page);
  //   setTextContent(page.text || "");
  //   setEditingText(false);
  //   setSelectedElement(null);
  // };

  const handleTextEdit = () => {
    setEditingText(true);
    setSelectedElement("text");
  };

  const handleSaveText = () => {
    setEditingText(false);
    // In a real implementation, this would update the page via API
    if (selectedPage) {
      selectedPage.text = textContent;
    }
  };

  if (!selectedPage && pages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-80 text-center">
        <div className="bg-gray-100 rounded-full p-6 mb-4">
          <Plus className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
          No pages yet
        </h3>
        <p className="text-gray-600 mb-4">
          Add your first page to start creating your book
        </p>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add First Page
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Canvas */}
      <div className="bg-white border border-gray-200 rounded-lg p-8 min-h-96 relative">
        <div className="text-center mb-6">
          <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
            Page Editor
          </h3>
          <p className="text-sm text-gray-600">
            Click to edit text, drag to rearrange elements
          </p>
        </div>

        {/* Main editing canvas with fairy tale forest background */}
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8 aspect-[4/3] relative overflow-hidden">
          {/* Background image area */}
          <div
            className={`w-full h-32 rounded-lg mb-4 bg-cover bg-center cursor-pointer transition-all ${
              selectedElement === "image"
                ? "ring-2 ring-primary ring-offset-2"
                : ""
            }`}
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600')`,
            }}
            onClick={() => setSelectedElement("image")}
          >
            {selectedElement === "image" && (
              <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded text-xs flex items-center">
                <Edit className="h-3 w-3 mr-1" />
                Edit Image
              </div>
            )}
          </div>

          {/* Text content area */}
          <div
            className={`bg-white/90 backdrop-blur-sm rounded-lg p-4 cursor-pointer transition-all ${
              selectedElement === "text"
                ? "ring-2 ring-primary ring-offset-2"
                : ""
            } ${editingText ? "bg-white" : ""}`}
            onClick={() => !editingText && setSelectedElement("text")}
          >
            {editingText ? (
              <div className="space-y-3">
                <Input
                  value="Chapter 1: The Magic Forest"
                  className="font-poppins text-lg font-semibold"
                  placeholder="Chapter title..."
                />
                <Textarea
                  value={textContent}
                  onChange={e => setTextContent(e.target.value)}
                  placeholder="Once upon a time..."
                  rows={4}
                  className="text-sm leading-relaxed resize-none"
                />
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleSaveText}>
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingText(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <h4 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
                  Chapter 1: The Magic Forest
                </h4>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {textContent ||
                    "Once upon a time, in a magical forest where the trees whispered secrets and the flowers danced in the wind, there lived a curious little rabbit named Luna..."}
                </p>
              </>
            )}
          </div>

          {/* Edit indicators */}
          {selectedElement === "text" && !editingText && (
            <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded text-xs flex items-center">
              <Edit className="h-3 w-3 mr-1" />
              Click to Edit
            </div>
          )}

          {/* Drag handle for selected elements */}
          {selectedElement && (
            <div className="absolute top-2 left-2 bg-gray-900 text-white px-2 py-1 rounded text-xs flex items-center drag-handle">
              <Move className="h-3 w-3 mr-1" />
              Drag
            </div>
          )}
        </div>

        {/* Page number indicator */}
        <div className="absolute bottom-4 left-4 text-sm text-gray-500">
          Page {selectedPage?.pageNumber || 1}
        </div>
      </div>

      {/* Editing Toolbar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {selectedElement
                  ? `${selectedElement} selected`
                  : "No selection"}
              </Badge>

              {selectedElement === "text" && (
                <div className="flex items-center space-x-1 border-l pl-2 ml-2">
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Underline className="h-4 w-4" />
                  </Button>
                  <div className="border-l pl-1 ml-1">
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <AlignLeft className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <AlignCenter className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <AlignRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {selectedElement === "text" ? (
                <Button size="sm" onClick={handleTextEdit}>
                  <Edit className="mr-1 h-4 w-4" />
                  Edit Text
                </Button>
              ) : selectedElement === "image" ? (
                <Button size="sm">
                  <Image className="mr-1 h-4 w-4" />
                  Replace Image
                </Button>
              ) : null}

              {selectedElement && (
                <Button size="sm" variant="outline">
                  <Trash2 className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tools Panel */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4 text-center">
            <Type className="h-8 w-8 text-primary mx-auto mb-2" />
            <h4 className="font-medium text-sm">Add Text</h4>
            <p className="text-xs text-gray-600">Insert text blocks</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4 text-center">
            <Image className="h-8 w-8 text-secondary mx-auto mb-2" />
            <h4 className="font-medium text-sm">Add Image</h4>
            <p className="text-xs text-gray-600">Upload or choose images</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4 text-center">
            <Shapes className="h-8 w-8 text-accent mx-auto mb-2" />
            <h4 className="font-medium text-sm">Add Shapes</h4>
            <p className="text-xs text-gray-600">Decorative elements</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4 text-center">
            <Palette className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <h4 className="font-medium text-sm">AI Design</h4>
            <p className="text-xs text-gray-600">Auto-enhance layout</p>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            {pages.length} page{pages.length !== 1 ? "s" : ""} total
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Eye className="mr-2 h-4 w-4" />
            Preview Book
          </Button>
          <Button size="sm" className="bg-secondary hover:bg-secondary/90">
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </div>
    </div>
  );
}
