# StoryWizard Azure Deployment Guide

## Prerequisites

### 1. Azure Account and Subscription

- Active Azure subscription with sufficient credits/billing
- Access to Azure Portal (https://portal.azure.com)
- Permissions to create resources (Contributor role or higher)

### 2. Local Linux Development Environment

- **Native Linux** (Ubuntu/Debian/Fedora/Arch) with:
  - Docker installed and running
  - Git installed
  - Code editor (VS Code recommended)
  - Web browser for Azure authentication
  - Internet connectivity

### 3. Docker Installation

Ensure Docker is properly installed and running:

```bash
# Verify Docker is installed and running
docker --version
docker compose --version
sudo systemctl status docker

# Add your user to docker group (if not already done)
sudo usermod -aG docker $USER
# Logout and login again for group changes to take effect

# Test Docker without sudo
docker run hello-world
```

### 4. Azure CLI Installation

Install Azure CLI on your local Linux machine:

```bash
# For Ubuntu/Debian
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# For Fedora/CentOS/RHEL
sudo dnf install -y azure-cli

# For Arch Linux
sudo pacman -S azure-cli

# Verify installation
az --version
```

### 5. Required Azure Resources

Create these before deployment:

- **Resource Group**: Container for all resources
- **Azure Container Registry (ACR)**: Private Docker registry

### 6. Environment Variables and Secrets

Required for production deployment:

- Azure Storage Account credentials (dev and prod)
- Database passwords
- JWT secret keys
- Stripe keys (optional)

## Deployment Process Overview

### Phase 1: Setup Azure Resources

1. Create Resource Group
2. Create Azure Container Registry
3. Create PostgreSQL container
4. Create application container

### Phase 2: Local Build and Deploy

1. Pull latest code from Git (local machine)
2. Test locally with Docker Compose
3. Build Docker image locally
4. Push to Azure Container Registry
5. Deploy containers to Azure Container Instances

### Phase 3: Configuration

1. Set environment variables
2. Configure database connections
3. Test deployment
4. Configure domain/SSL (if needed)

## Commands Summary

### Azure Resource Creation

```bash
# Create resource group
az group create --name storywizard-rg --location eastus

# Create container registry
az acr create --resource-group storywizard-rg --name storywizardregistry --sku Basic

# Get registry credentials (save these for later use)
az acr credential show --name storywizardregistry
```

### Initial Build and Deploy (First Time Only)

```bash
# Login to Azure from your local Linux machine
az login --use-device-code
az acr login --name storywizardregistry

# Navigate to your project directory
cd /media/dadmin/Data/git/StoryWizard

# Test locally first
docker compose down && docker compose up --build -d
# Test the application at http://localhost:5000
# Stop local containers when done testing
docker compose down

# Build and push app image
docker build -t storywizardregistry.azurecr.io/storywizard:latest .
docker push storywizardregistry.azurecr.io/storywizard:latest

# CRITICAL: Push PostgreSQL to your own registry (Azure has Docker Hub connectivity issues)
docker pull postgres:15-alpine
docker tag postgres:15-alpine storywizardregistry.azurecr.io/postgres:15-alpine
docker push storywizardregistry.azurecr.io/postgres:15-alpine

# Deploy PostgreSQL container ONCE with PUBLIC IP (this stays running)
az container create \
    --resource-group storywizard-rg \
    --name storywizard-postgres \
    --image storywizardregistry.azurecr.io/postgres:15-alpine \
    --os-type Linux \
    --registry-username storywizardregistry \
    --registry-password "YOUR_REGISTRY_PASSWORD" \
    --environment-variables POSTGRES_DB=storywizard POSTGRES_USER=storywizard POSTGRES_PASSWORD=sdagfedsg54ewg3 \
    --ports 5432 \
    --cpu 1 \
    --memory 1 \
    --ip-address Public

# Get PostgreSQL IP (SAVE THIS - you'll need it for all app deployments)
az container show --resource-group storywizard-rg --name storywizard-postgres --query ipAddress.ip --output tsv

# Deploy app container with PUBLIC IP (using actual PostgreSQL IP from above)
az container create \
    --resource-group storywizard-rg \
    --name storywizard-app \
    --image storywizardregistry.azurecr.io/storywizard:latest \
    --os-type Linux \
    --registry-username storywizardregistry \
    --registry-password "YOUR_REGISTRY_PASSWORD" \
    --environment-variables NODE_ENV=production DATABASE_URL="********************************************************************/storywizard" AZURE_STORAGE_ACCOUNT_NAME_PROD=factorysharedstorage AZURE_STORAGE_ACCOUNT_KEY_PROD="YOUR_PROD_AZURE_STORAGE_KEY" \
    --ports 5000 \
    --cpu 1 \
    --memory 1 \
    --ip-address Public

# Get app IP
az container show --resource-group storywizard-rg --name storywizard-app --query ipAddress.ip --output tsv
```

### Regular Code Deployments (After Initial Setup)

```bash
# 1. Navigate to project directory and get latest code
cd /media/dadmin/Data/git/StoryWizard
git pull origin master  # or your main branch

# 2. Test locally first (ALWAYS do this)
docker compose down && docker compose up --build -d
# Test the application at http://localhost:5000
# Run any tests: npm run test:run
# Stop local containers when done testing
docker compose down

# 3. If local tests pass, build and push to Azure
docker build -t storywizardregistry.azurecr.io/storywizard:latest .
docker push storywizardregistry.azurecr.io/storywizard:latest

# 4. Delete only the app container (leave PostgreSQL running for data persistence)
az container delete --resource-group storywizard-rg --name storywizard-app --yes

# 5. Recreate app container with updated image
# Replace POSTGRES_IP_HERE with your actual PostgreSQL container IP
az container create \
    --resource-group storywizard-rg \
    --name storywizard-app \
    --image storywizardregistry.azurecr.io/storywizard:latest \
    --os-type Linux \
    --registry-username storywizardregistry \
    --registry-password "YOUR_REGISTRY_PASSWORD" \
    --environment-variables NODE_ENV=production DATABASE_URL="**************************************************************/storywizard" AZURE_STORAGE_ACCOUNT_NAME_PROD=factorysharedstorage AZURE_STORAGE_ACCOUNT_KEY_PROD="YOUR_PROD_AZURE_STORAGE_KEY" \
    --ports 5000 \
    --cpu 1 \
    --memory 1 \
    --ip-address Public

# 6. Get new app IP and test deployment
az container show --resource-group storywizard-rg --name storywizard-app --query ipAddress.ip --output tsv
# Test at http://NEW_APP_IP:5000
```

## Important Notes

1. **Docker Hub Connectivity Issues**: Azure Container Instances frequently has connectivity problems with Docker Hub. This is a known Azure infrastructure issue. ALWAYS push ALL images (including base images like postgres) to your own Azure Container Registry.

2. **DATABASE_URL Parsing**: The Dockerfile startup script now properly parses the DATABASE_URL environment variable to extract the database host. This was initially hardcoded to `postgres:5432` which only worked in Docker Compose, causing production deployment failures.

3. **Public IP Required**: ALWAYS include `--ip-address Public` or containers won't be accessible from outside Azure.

4. **Container Communication**: Azure Container Instances in different container groups cannot communicate by container name. They must use actual IP addresses in connection strings.

5. **PostgreSQL Persistence**: The PostgreSQL container should be deployed ONCE and left running. Only redeploy the app container for code changes. The database retains all data between app deployments.

6. **Registry Authentication**: Always use `--registry-username` and `--registry-password` when pulling from Azure Container Registry.

7. **Multi-line Commands**: Azure CLI commands are now formatted with backslashes for readability. You can copy-paste these directly into your Linux terminal.

8. **Cost Management**:
   - Monthly cost: ~$35-55 for 24/7 operation
   - Use Azure Portal to start/stop containers to save costs
   - Only pay for running time when containers are stopped

9. **Local Testing**: ALWAYS test locally with `docker compose up --build -d` before deploying to Azure. This ensures your changes work correctly in a containerized environment.

10. **Security**: Never commit secrets to Git. Use Azure Key Vault for production secrets.

11. **Scaling**: For production load, consider upgrading to Azure App Service or Azure Kubernetes Service.

12. **Monitoring**: Set up Azure Monitor and Application Insights for production monitoring.

13. **Backups**: Implement database backup strategy for production data.

## Troubleshooting

### Common Issues:

#### "RegistryErrorResponse from docker.io"

- **Cause**: Azure has connectivity issues with Docker Hub
- **Solution**: Push all images to your own Azure Container Registry first

#### "Container won't connect to database"

- **Cause**: Hardcoded database host or missing public IP
- **Solution**: Ensure DATABASE_URL uses actual IP address (***********:5432)

#### "Container has no public IP"

- **Cause**: Missing `--ip-address Public` flag
- **Solution**: Delete and recreate container with public IP flag

#### "Registry authentication failed"

- **Cause**: Wrong or missing registry credentials
- **Solution**: Get fresh credentials with `az acr credential show --name storywizardregistry`

#### "Multi-line command execution issues"

- **Cause**: Terminal doesn't handle multi-line commands properly
- **Solution**: Commands are now formatted with backslashes for Linux terminals

### Useful Commands:

```bash
# Check what's actually deployed
az resource list --resource-group storywizard-rg --output table

# Check container status
az container show --resource-group storywizard-rg --name storywizard-app --query instanceView.state

# View container logs
az container logs --resource-group storywizard-rg --name storywizard-app

# View PostgreSQL logs
az container logs --resource-group storywizard-rg --name storywizard-postgres

# Get container IPs
az container show --resource-group storywizard-rg --name storywizard-app --query ipAddress.ip --output tsv
az container show --resource-group storywizard-rg --name storywizard-postgres --query ipAddress.ip --output tsv

# Stop containers (save costs)
az container stop --resource-group storywizard-rg --name storywizard-app
az container stop --resource-group storywizard-rg --name storywizard-postgres

# Start containers
az container start --resource-group storywizard-rg --name storywizard-app
az container start --resource-group storywizard-rg --name storywizard-postgres
```

## Cost Optimization

### Development/Testing:

- Use Basic tier for Container Registry (~$5/month)
- Use smallest container instance sizes
- Stop containers when not in use

### Production:

- Monitor usage with Azure Cost Management
- Consider reserved instances for predictable workloads
- Set up billing alerts

## Linux Development Workflow

### Daily Development Cycle

```bash
# 1. Start your development session
cd /media/dadmin/Data/git/StoryWizard
git status
git pull origin master

# 2. Make your code changes
code .  # Open in VS Code

# 3. Test locally
npm run dev  # Development server
# OR test with Docker
docker compose up --build -d

# 4. Run quality checks
npm run validate  # Runs lint, format, type-check, tests

# 5. Commit changes (if satisfied)
git add .
git commit -m "Your commit message"
git push origin master

# 6. Deploy to Azure (when ready)
# Follow the "Regular Code Deployments" section above
```

### Local Environment Management

```bash
# Check Docker status
sudo systemctl status docker

# Restart Docker if needed
sudo systemctl restart docker

# Clean up Docker resources (when disk space is low)
docker system prune -af
docker volume prune -f

# Monitor container resources
docker stats

# View container logs
docker compose logs -f
```

## Next Steps

After successful deployment:

1. Configure custom domain and SSL certificate
2. Set up continuous deployment pipeline (GitHub Actions)
3. Implement monitoring and alerting
4. Configure automated backups
5. Set up staging environment
6. Configure Linux cron jobs for automated deployments
