{"version": "0.2.0", "configurations": [{"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/index.ts", "env": {"NODE_ENV": "development"}, "runtimeExecutable": "npx", "runtimeArgs": ["tsx"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env"}, {"name": "Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal"}]}