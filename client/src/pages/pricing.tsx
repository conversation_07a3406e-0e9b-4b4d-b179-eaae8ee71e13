import { Check, X } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";
import { useAuth } from "@/lib/auth";

export default function Pricing() {
  const { user } = useAuth();

  const plans = [
    {
      name: "Free",
      price: 0,
      period: "month",
      description: "Perfect for trying out StoryBook AI",
      features: [
        { name: "1 book per month", included: true },
        { name: "AI-powered design", included: true },
        { name: "Basic templates", included: true },
        { name: "Watermarked exports", included: false },
        { name: "HD PDF downloads", included: false },
        { name: "Commercial rights", included: false },
      ],
      cta: "Get Started",
      popular: false,
      current: user?.subscriptionTier === "free",
    },
    {
      name: "Basic",
      price: 9,
      period: "month",
      description: "Great for regular book creators",
      features: [
        { name: "5 books per month", included: true },
        { name: "HD PDF downloads", included: true },
        { name: "Premium templates", included: true },
        { name: "No watermarks", included: true },
        { name: "Priority support", included: false },
        { name: "Commercial rights", included: false },
      ],
      cta: "Choose Basic",
      popular: true,
      current: user?.subscriptionTier === "basic",
    },
    {
      name: "Pro",
      price: 29,
      period: "month",
      description: "For professional authors and publishers",
      features: [
        { name: "Unlimited books", included: true },
        { name: "Commercial rights", included: true },
        { name: "Priority support", included: true },
        { name: "Advanced AI features", included: true },
        { name: "Custom branding", included: true },
        { name: "API access", included: true },
      ],
      cta: "Choose Pro",
      popular: false,
      current: user?.subscriptionTier === "pro",
    },
  ];

  const handleSubscribe = (planName: string) => {
    if (!user) {
      return "/auth?mode=signup";
    }
    return `/subscribe?plan=${planName.toLowerCase()}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="font-poppins text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Start free and upgrade as your creativity grows. All plans include
              AI-powered design and secure storage.
            </p>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map(plan => (
            <Card
              key={plan.name}
              className={`relative ${plan.popular ? "border-primary shadow-lg scale-105" : ""} ${plan.current ? "bg-blue-50 border-blue-200" : ""}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-white">Most Popular</Badge>
                </div>
              )}
              {plan.current && (
                <div className="absolute -top-4 right-4">
                  <Badge variant="secondary">Current Plan</Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold">
                  {plan.name}
                </CardTitle>
                <div className="text-4xl font-bold text-gray-900 my-4">
                  ${plan.price}
                  <span className="text-lg font-normal text-gray-600">
                    /{plan.period}
                  </span>
                </div>
                <p className="text-gray-600">{plan.description}</p>
              </CardHeader>

              <CardContent>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      {feature.included ? (
                        <Check className="h-5 w-5 text-green-500 mr-3" />
                      ) : (
                        <X className="h-5 w-5 text-gray-400 mr-3" />
                      )}
                      <span
                        className={
                          feature.included ? "text-gray-700" : "text-gray-500"
                        }
                      >
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>

                <Link href={handleSubscribe(plan.name)}>
                  <Button
                    className={`w-full ${plan.popular ? "bg-primary hover:bg-primary/90" : plan.name === "Pro" ? "bg-accent hover:bg-accent/90" : "bg-gray-200 text-gray-700 hover:bg-gray-300"}`}
                    disabled={plan.current}
                  >
                    {plan.current ? "Current Plan" : plan.cta}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <h2 className="font-poppins text-3xl font-bold text-gray-900 text-center mb-12">
            Frequently Asked Questions
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Can I change my plan anytime?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Yes, you can upgrade or downgrade your plan at any time.
                  Changes take effect immediately.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  What happens to my books if I downgrade?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Your existing books remain accessible, but you&apos;ll be
                  limited to your new plan&apos;s monthly creation limit.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Are there any setup fees?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  No, there are no setup fees. You only pay the monthly
                  subscription fee for your chosen plan.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Can I use my books commercially?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Commercial rights are included with the Pro plan. Free and
                  Basic plans are for personal use only.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
