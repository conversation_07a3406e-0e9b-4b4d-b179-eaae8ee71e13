# WSL2 Development Setup Guide for StoryWizard

## 🚨 WSL2 Networking Issue & Solutions

### **The Problem**

WSL2 runs in a virtual machine with its own network stack. When you run `npm run dev` and see "serving on port 5000", the server is only accessible within WSL2, not from your Windows browser at `localhost:5000`.

### **Quick Fix: Use WSL2 IP Address**

#### **Step 1: Get Your WSL2 IP Address**

```bash
# Run this in WSL2 terminal to get your current WSL IP
hostname -I
```

Example output: `************`

#### **Step 2: Start Your Development Server**

```bash
npm run dev
```

Wait for: `serving on port 5000`

#### **Step 3: Access from Windows Browser**

Use your WSL2 IP instead of localhost:

```
http://************:5000
```

Replace `************` with your actual WSL2 IP from Step 1.

---

## 🔧 Permanent Solutions

### **Option A: Port Forwarding (Recommended)**

Run this in **Windows PowerShell as Administrator** to forward localhost:5000 to WSL:

```powershell
# Forward Windows localhost:5000 to WSL2
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=************

# Verify it's working
netsh interface portproxy show all
```

**To remove later:**

```powershell
netsh interface portproxy delete v4tov4 listenport=5000 listenaddress=0.0.0.0
```

After this, you can use `http://localhost:5000` in Windows browser.

### **Option B: Windows Hosts File**

Add a friendly hostname to `C:\Windows\System32\drivers\etc\hosts`:

```
************ storywizard.local
```

Then access: `http://storywizard.local:5000`

### **Option C: WSL2 Configuration (Advanced)**

Create/edit `C:\Users\<USER>\.wslconfig`:

```ini
[wsl2]
networkingMode=mirrored
localhostForwarding=true
```

Restart WSL2:

```powershell
wsl --shutdown
wsl
```

---

## 📋 Quick Reference Commands

### **Get WSL2 Network Info**

```bash
# Your WSL2 IP
hostname -I

# Windows host IP (from WSL2)
ip route show | grep default | awk '{print $3}'

# Check if server is running
curl -s http://$(hostname -I | awk '{print $1}'):5000 | head -5
```

### **Test Server Status**

```bash
# Check if port 5000 is listening in WSL2
netstat -tlnp | grep 5000

# Test local access
curl -I http://localhost:5000

# Test WSL2 IP access
curl -I http://$(hostname -I | awk '{print $1}'):5000
```

### **Windows PowerShell Port Management**

```powershell
# List all port forwards
netsh interface portproxy show all

# Add port forward
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=<WSL2_IP>

# Remove port forward
netsh interface portproxy delete v4tov4 listenport=5000 listenaddress=0.0.0.0

# Remove all port forwards
netsh interface portproxy reset
```

---

## 🛠️ Development Workflow

### **Daily Development Steps:**

1. **Start WSL2 terminal**
2. **Navigate to project**: `cd /mnt/c/AFJ/StoryWizard`
3. **Get current WSL2 IP**: `hostname -I`
4. **Start development server**: `npm run dev`
5. **Open Windows browser to**: `http://<WSL2_IP>:5000`

### **One-Time Setup (Choose One):**

- **Easy**: Use WSL2 IP directly each time
- **Convenient**: Set up port forwarding once
- **Professional**: Use hosts file with custom domain

---

## 🐛 Troubleshooting

### **"Connection Refused" Error**

```bash
# Check if server is actually running
ps aux | grep tsx

# Check if port is listening
netstat -tlnp | grep 5000

# Restart the development server
pkill -f tsx
npm run dev
```

### **WSL2 IP Changed**

WSL2 IP addresses can change on restart. If your saved IP stops working:

```bash
hostname -I  # Get new IP
```

Update your port forwarding or bookmark with the new IP.

### **Port Already in Use**

```bash
# Find what's using port 5000
netstat -tlnp | grep 5000

# Kill the process
pkill -f tsx
# or
kill -9 <PID>
```

### **Firewall Issues**

If Windows Firewall blocks access, allow Node.js through Windows Defender Firewall settings.

---

## 📝 Environment Notes

- **OS**: WSL2 on Windows
- **Network Mode**: Default WSL2 (NAT)
- **Development Server**: Express + Vite
- **Default Port**: 5000
- **Expected Behavior**: Server accessible via WSL2 IP, not localhost

---

## 🎯 Quick Success Test

Run this complete test to verify everything works:

```bash
# 1. Get IP and start server
WSL_IP=$(hostname -I | awk '{print $1}')
echo "WSL2 IP: $WSL_IP"
npm run dev &

# 2. Wait a moment for startup
sleep 10

# 3. Test access
curl -I http://$WSL_IP:5000

# 4. Show browser URL
echo "Open this in Windows browser: http://$WSL_IP:5000"
```

If you see HTML response and a URL, you're ready to develop! 🚀

---

## 📧 Share This Guide

Save this file as `WSL2-DEVELOPMENT-GUIDE.md` and share with team members who use WSL2 for development. The core issue affects all Node.js/web development in WSL2, not just StoryWizard.
