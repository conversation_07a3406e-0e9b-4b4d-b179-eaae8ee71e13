import {
  Book,
  Eye,
  WandSparkles,
  Palette,
  Smartphone,
  FileText,
  Shield,
  Play,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Link } from "wouter";
import { useAuth } from "@/lib/auth";
import { BookCreationInterface } from "@/components/book-creation-interface";

export default function Home() {
  const { user } = useAuth();

  const features = [
    {
      icon: WandSparkles,
      title: "AI-Powered Design",
      description:
        "Our AI analyzes your images and automatically creates beautiful layouts with perfect typography and spacing.",
      color: "text-primary",
    },
    {
      icon: Eye,
      title: "Smart Image Enhancement",
      description:
        "Automatically crop, enhance, and apply artistic effects to make your photos look professional.",
      color: "text-secondary",
    },
    {
      icon: Palette,
      title: "Cohesive Color Schemes",
      description:
        "Generate beautiful color palettes that work across all pages for a consistent, professional look.",
      color: "text-accent",
    },
    {
      icon: Smartphone,
      title: "Drag & Drop Interface",
      description:
        "Intuitive editor that works on any device. Arrange pages, add text, and customize with ease.",
      color: "text-primary",
    },
    {
      icon: FileText,
      title: "Professional Export",
      description:
        "Export high-resolution PDFs ready for printing or digital sharing with perfect quality.",
      color: "text-secondary",
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description:
        "Your content is encrypted and private. Each user has their own secure space with no data sharing.",
      color: "text-accent",
    },
  ];

  const examples = [
    {
      title: "Luna's Space Adventure",
      description:
        "A magical journey through the stars with beautiful illustrations and engaging storytelling.",
      age: "Age 4-8",
      image:
        "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=400",
    },
    {
      title: "Forest Friends",
      description:
        "Meet the wonderful animals that live in the enchanted forest and learn about friendship.",
      age: "Age 3-7",
      image:
        "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=400",
    },
    {
      title: "Under the Sea",
      description:
        "Dive deep into the ocean and discover amazing creatures in this underwater adventure.",
      age: "Age 5-9",
      image:
        "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=400",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Book className="text-primary text-2xl mr-3" />
                <span className="font-poppins font-bold text-xl text-gray-900">
                  StoryBook AI
                </span>
              </div>
            </div>

            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a
                  href="#features"
                  className="text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                >
                  Features
                </a>
                <Link
                  href="/pricing"
                  className="text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                >
                  Pricing
                </Link>
                <a
                  href="#examples"
                  className="text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                >
                  Examples
                </a>
                {user ? (
                  <Link href="/dashboard">
                    <Button variant="default">Dashboard</Button>
                  </Link>
                ) : (
                  <>
                    <Link href="/auth?mode=login">
                      <Button variant="ghost">Log In</Button>
                    </Link>
                    <Link href="/auth?mode=signup">
                      <Button variant="default">Get Started</Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="gradient-hero py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-poppins text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Create Beautiful{" "}
              <span className="text-primary">Children&apos;s Books</span> in
              Minutes
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Turn your photos and stories into professional-quality
              children&apos;s books with AI-powered design, layout, and
              enhancement tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth?mode=signup">
                <Button size="lg" className="text-lg px-8 py-4">
                  Start Creating Free
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-poppins text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Create Amazing Books
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our AI-powered platform handles the design work, so you can focus
              on telling your story.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-gray-50 border-none">
                <CardContent className="p-8 text-center">
                  <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className={`${feature.color} text-2xl`} />
                  </div>
                  <h3 className="font-poppins text-xl font-semibold text-gray-900 mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Book Creation Interface Preview */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-poppins text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Powerful Creation Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Upload your photos, add your story, and let our AI create a
              beautiful book layout automatically.
            </p>
          </div>

          <BookCreationInterface />
        </div>
      </section>

      {/* Examples Section */}
      <section id="examples" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-poppins text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Beautiful Books Made by Our Users
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See what&apos;s possible when you combine your creativity with our
              AI-powered design tools.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {examples.map((example, index) => (
              <Card key={index} className="book-shadow overflow-hidden">
                <img
                  src={example.image}
                  alt={example.title}
                  className="w-full h-48 object-cover"
                />
                <CardContent className="p-6">
                  <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
                    {example.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {example.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{example.age}</span>
                    <Button variant="ghost" size="sm">
                      Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <Book className="text-primary text-2xl mr-3" />
                <span className="font-poppins font-bold text-xl">
                  StoryBook AI
                </span>
              </div>
              <p className="text-gray-400 mb-4">
                Create beautiful, professional children&apos;s books with
                AI-powered design tools. Turn your photos and stories into
                magical experiences.
              </p>
            </div>

            <div>
              <h4 className="font-poppins font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a
                    href="#features"
                    className="hover:text-white transition-colors"
                  >
                    Features
                  </a>
                </li>
                <li>
                  <Link
                    href="/pricing"
                    className="hover:text-white transition-colors"
                  >
                    Pricing
                  </Link>
                </li>
                <li>
                  <a
                    href="#examples"
                    className="hover:text-white transition-colors"
                  >
                    Examples
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-poppins font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Privacy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Terms
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 StoryBook AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
