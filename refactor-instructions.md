# Refactor Task for Claude Code

## Source Project

- **Framework**: React with TypeScript (frontend) + Node.js/Express (backend)
- **Current Database**: PostgreSQL with Drizzle ORM (configured for Neon Database)
- **Features**: User authentication (JWT), book creation/editing, file uploads, PDF generation, Stripe payment integration, subscription management (3 tiers: Free/Basic/Pro)
- **Files**: Full-stack application with React frontend, Express API backend, PostgreSQL schema, Stripe integration, file upload system, PDF export functionality

## Target Requirements

- **Database**: Supabase PostgreSQL (migrate from Neon, keep Drizzle ORM)
- **Deployment**: Docker containerization + Azure App Service (full-stack deployment)
- **Framework**: Keep React + TypeScript frontend, keep Node.js/Express backend
- **Features to preserve**: All - User authentication (JWT), book creation/editing, file uploads, PDF generation, Stripe payments, subscription management (Free/Basic/Pro tiers)

## Specific Tasks

1. Analyze current code structure
2. Set up proper package.json and dependencies
3. Create Supabase database schema
4. Refactor data layer to use Supabase
5. Set up Docker configuration
6. Create deployment scripts
7. Add proper error handling and validation
8. Set up environment configuration
9. Create documentation

## Constraints

- Budget: $50/month for hosting
- Timeline: ASAP
- Must maintain existing UI/UX
