import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, CheckCircle, XCircle, Eye } from "lucide-react";

interface ContentItem {
  id: number;
  type: 'book' | 'page' | 'file';
  userId: number;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  content: any;
  createdAt: string;
}

interface ModerationStats {
  pending: number;
  approved: number;
  rejected: number;
  flagged: number;
  totalReviewed: number;
}

export default function AdminModerationPage() {
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [showDetails, setShowDetails] = useState<number | null>(null);

  const queryClient = useQueryClient();

  // Fetch moderation queue
  const { data: contentQueue, isLoading, error } = useQuery({
    queryKey: ['admin', 'moderation', 'queue'],
    queryFn: async () => {
      const response = await fetch('/api/admin/moderation/queue', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('Access denied. Admin privileges required.');
        }
        throw new Error('Failed to fetch moderation queue');
      }

      return response.json() as Promise<ContentItem[]>;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch moderation stats
  const { data: stats } = useQuery({
    queryKey: ['admin', 'moderation', 'stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/moderation/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch moderation stats');
      }

      return response.json() as Promise<ModerationStats>;
    },
  });

  // Approve content mutation
  const approveMutation = useMutation({
    mutationFn: async ({ contentType, contentId, notes }: { contentType: string; contentId: number; notes?: string }) => {
      const response = await fetch(`/api/admin/moderation/${contentType}/${contentId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve content');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'moderation'] });
      setSelectedContent(null);
      setReviewNotes('');
    },
  });

  // Reject content mutation
  const rejectMutation = useMutation({
    mutationFn: async ({ 
      contentType, 
      contentId, 
      reason, 
      notes 
    }: { 
      contentType: string; 
      contentId: number; 
      reason: string;
      notes?: string;
    }) => {
      const response = await fetch(`/api/admin/moderation/${contentType}/${contentId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ reason, notes }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject content');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'moderation'] });
      setSelectedContent(null);
      setReviewNotes('');
      setRejectionReason('');
    },
  });

  const handleApprove = () => {
    if (!selectedContent) return;
    
    approveMutation.mutate({
      contentType: selectedContent.type,
      contentId: selectedContent.id,
      notes: reviewNotes,
    });
  };

  const handleReject = () => {
    if (!selectedContent || !rejectionReason.trim()) return;
    
    rejectMutation.mutate({
      contentType: selectedContent.type,
      contentId: selectedContent.id,
      reason: rejectionReason,
      notes: reviewNotes,
    });
  };

  const renderContentPreview = (item: ContentItem) => {
    switch (item.type) {
      case 'book':
        return (
          <div>
            <h4 className="font-semibold">{item.content.title}</h4>
            {item.content.description && (
              <p className="text-sm text-gray-600 mt-1">{item.content.description}</p>
            )}
            {item.content.coverImage && (
              <img 
                src={item.content.coverImage} 
                alt="Cover" 
                className="w-16 h-20 object-cover mt-2 rounded"
              />
            )}
          </div>
        );
      
      case 'page':
        return (
          <div>
            <p className="text-sm text-gray-600">Page {item.content.pageNumber}</p>
            {item.content.text && (
              <p className="text-sm mt-1 line-clamp-3">{item.content.text}</p>
            )}
            {item.content.imageUrl && (
              <img 
                src={item.content.imageUrl} 
                alt="Page content" 
                className="w-16 h-20 object-cover mt-2 rounded"
              />
            )}
          </div>
        );
      
      case 'file':
        return (
          <div>
            <p className="font-semibold text-sm">{item.content.originalName}</p>
            <p className="text-xs text-gray-500">{item.content.mimetype}</p>
            {item.content.path && item.content.mimetype?.startsWith('image/') && (
              <img 
                src={item.content.path} 
                alt="Uploaded file" 
                className="w-16 h-20 object-cover mt-2 rounded"
              />
            )}
          </div>
        );
      
      default:
        return <p className="text-sm text-gray-500">Unknown content type</p>;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'flagged': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error instanceof Error ? error.message : 'Failed to load moderation dashboard'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Content Moderation Dashboard</h1>
        <p className="text-gray-600">Review and moderate user-generated content</p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-yellow-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{stats.flagged}</p>
                  <p className="text-sm text-gray-600">Needs Review</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Eye className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{stats.approved}</p>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{stats.rejected}</p>
                  <p className="text-sm text-gray-600">Rejected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <span className="text-blue-600 font-bold text-sm">T</span>
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalReviewed}</p>
                  <p className="text-sm text-gray-600">Total Reviewed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Content Review Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Content Queue */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Content Review Queue</CardTitle>
              <CardDescription>
                Content flagged for manual review
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading...</div>
              ) : !contentQueue || contentQueue.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No content pending review
                </div>
              ) : (
                <div className="space-y-3">
                  {contentQueue.map((item) => (
                    <div
                      key={`${item.type}-${item.id}`}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedContent?.id === item.id && selectedContent?.type === item.type
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedContent(item)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{item.type}</Badge>
                          <Badge className={getStatusColor(item.status)}>
                            {item.status}
                          </Badge>
                        </div>
                        <span className="text-xs text-gray-500">
                          User ID: {item.userId}
                        </span>
                      </div>
                      {renderContentPreview(item)}
                      <div className="text-xs text-gray-400 mt-2">
                        Created: {new Date(item.createdAt).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Review Panel */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Review Panel</CardTitle>
              <CardDescription>
                {selectedContent ? 'Take action on selected content' : 'Select content to review'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedContent ? (
                <>
                  <div className="p-3 bg-gray-50 rounded">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">{selectedContent.type}</Badge>
                      <Badge className={getStatusColor(selectedContent.status)}>
                        {selectedContent.status}
                      </Badge>
                    </div>
                    {renderContentPreview(selectedContent)}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium">Review Notes</label>
                      <Textarea
                        placeholder="Add notes about your decision..."
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Rejection Reason</label>
                      <Textarea
                        placeholder="Required if rejecting content..."
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  Select content from the queue to review
                </p>
              )}
            </CardContent>
            {selectedContent && (
              <CardFooter className="space-x-2">
                <Button
                  onClick={handleApprove}
                  disabled={approveMutation.isPending}
                  className="flex-1"
                  variant="default"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Approve
                </Button>
                <Button
                  onClick={handleReject}
                  disabled={rejectMutation.isPending || !rejectionReason.trim()}
                  className="flex-1"
                  variant="destructive"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Reject
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}