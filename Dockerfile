# Simple single-stage build for the cleaned up StoryWizard app
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (using --ignore-scripts to avoid native binary issues)
RUN npm install --ignore-scripts

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S storywizard -u 1001

# Change ownership of app directory
RUN chown -R storywizard:nodejs /app

# Switch to non-root user
USER storywizard

# Expose port (using 3001 to match our simple server)
EXPOSE 3001

# Health check (simplified for our basic setup)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Start the application using our simple server
CMD ["npm", "start"]