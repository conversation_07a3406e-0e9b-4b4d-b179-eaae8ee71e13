# Multi-stage build for optimized production image
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Install dumb-init and postgresql-client for proper signal handling and db connectivity checks
RUN apk add --no-cache dumb-init postgresql-client

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S storywizard -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies and drizzle-kit for migrations
RUN npm ci --omit=dev && npm install drizzle-kit && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=storywizard:nodejs /app/dist ./dist
COPY --from=builder --chown=storywizard:nodejs /app/server ./server
COPY --from=builder --chown=storywizard:nodejs /app/shared ./shared

# Copy other necessary files
COPY --chown=storywizard:nodejs drizzle.config.ts ./
COPY --chown=storywizard:nodejs tsconfig.json ./

# Create scripts directory and copy startup script
RUN mkdir -p scripts
COPY --chown=storywizard:nodejs scripts/docker-start.sh ./scripts/
RUN chmod +x ./scripts/docker-start.sh

# Create logs directory
RUN mkdir -p /app/logs && chown storywizard:nodejs /app/logs

# Switch to non-root user
USER storywizard

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Use direct command instead of script (script file copy issue)
CMD ["sh", "-c", "echo '🚀 Starting StoryWizard...' && while ! pg_isready -h postgres -p 5432 -U storywizard; do echo 'Waiting for PostgreSQL...'; sleep 2; done && echo '📊 Running migrations...' && npx drizzle-kit push --force && echo '🌟 Starting application...' && npm start"]