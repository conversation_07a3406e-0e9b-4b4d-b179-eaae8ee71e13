# StoryBook AI - Replit Configuration

## Overview

StoryBook AI is a modern full-stack web application for creating AI-powered photo books. Users can upload images, generate books with AI-assisted design, and export them as PDFs. The application features a subscription-based model with different tiers (free, basic, pro) and integrates with Stripe for payment processing.

## System Architecture

### Frontend Architecture

- **Framework**: React with TypeScript
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack React Query for server state management
- **Styling**: Tailwind CSS with CSS variables for theming
- **UI Components**: Radix UI primitives with custom component library (shadcn/ui)
- **Authentication**: Context-based authentication with JWT tokens
- **Payment Integration**: Stripe React components for subscription management

### Backend Architecture

- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Authentication**: JWT-based authentication with bcrypt for password hashing
- **File Handling**: Multer for file uploads with local storage
- **PDF Generation**: PDFKit for creating downloadable books
- **API Design**: RESTful API with proper error handling and logging middleware

### Data Storage

- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (@neondatabase/serverless)
- **Schema Management**: Drizzle Kit for migrations and schema management
- **In-Memory Fallback**: Memory-based storage implementation for development

### Build System

- **Frontend Build**: Vite with React plugin
- **Backend Build**: esbuild for server bundling
- **Development**: Hot module replacement with Vite dev server
- **TypeScript**: Strict type checking with path mapping

## Key Components

### Database Schema

- **Users**: Authentication, subscription management, usage tracking
- **Books**: User-generated photo books with metadata and status
- **Book Pages**: Individual pages with images, text, and layout configuration
- **User Files**: File upload tracking and metadata storage

### Authentication System

- JWT-based authentication with secure token storage
- Password hashing with bcrypt
- Protected routes and middleware
- User session management

### File Management

- Multer-based file upload system
- User-specific directory structure
- File type validation and size limits
- Secure file serving with proper access controls

### Subscription System

- Stripe integration for payment processing
- Three-tier subscription model (free, basic, pro)
- Usage tracking and limits enforcement
- Webhook handling for subscription events

### PDF Generation

- PDFKit integration for book export
- Custom layout rendering
- Image embedding and text placement
- Professional formatting options

## Data Flow

1. **User Registration/Login**: Users authenticate through JWT-based system
2. **File Upload**: Images are uploaded to user-specific directories
3. **Book Creation**: Users create books with uploaded images
4. **AI Processing**: Future integration point for AI-powered layout generation
5. **Page Editing**: Users can edit individual pages with text and layout options
6. **PDF Export**: Books are rendered as downloadable PDFs
7. **Subscription Management**: Stripe handles payment processing and subscription updates

## External Dependencies

### Core Dependencies

- **Database**: Neon PostgreSQL with Drizzle ORM
- **Payment Processing**: Stripe for subscriptions and payments
- **File Storage**: Local file system (configurable for cloud storage)
- **Authentication**: JWT tokens with bcrypt hashing

### Development Dependencies

- **Vite**: Frontend build tool and dev server
- **TypeScript**: Type checking and compilation
- **ESLint/Prettier**: Code formatting and linting
- **Tailwind CSS**: Utility-first CSS framework

### UI Dependencies

- **Radix UI**: Headless UI components
- **Lucide React**: Icon library
- **React Hook Form**: Form management
- **Zod**: Schema validation

## Deployment Strategy

### Production Build

- Frontend: Vite builds optimized React application
- Backend: esbuild creates single bundle for Node.js deployment
- Static assets served from dist/public directory
- Environment variables for configuration

### Development Environment

- Vite dev server with HMR for frontend development
- tsx for TypeScript execution in development
- Concurrent frontend and backend development
- Database migrations with Drizzle Kit

### Environment Configuration

- DATABASE_URL: PostgreSQL connection string
- JWT_SECRET: Secret key for JWT token signing
- STRIPE_SECRET_KEY: Stripe API secret key
- VITE_STRIPE_PUBLIC_KEY: Stripe public key for frontend

## User Preferences

Preferred communication style: Simple, everyday language.

## Changelog

Changelog:

- July 04, 2025. Initial setup
