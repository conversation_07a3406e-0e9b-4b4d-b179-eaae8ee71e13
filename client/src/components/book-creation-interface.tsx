import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Eye, Download, Edit, Type, Image, Shapes } from "lucide-react";

export function BookCreationInterface() {
  const mockPages = [
    {
      id: 1,
      title: "Cover Page",
      image:
        "https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      isActive: true,
    },
    {
      id: 2,
      title: "Page 1",
      image:
        "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      isActive: false,
    },
    {
      id: 3,
      title: "Page 2",
      image:
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      isActive: false,
    },
  ];

  return (
    <Card className="bg-white shadow-xl">
      <CardContent className="p-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Pages */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-4">
                Your Pages
              </h3>
              <div className="space-y-4">
                {mockPages.map(page => (
                  <div
                    key={page.id}
                    className={`bg-white rounded-lg p-4 border-2 ${
                      page.isActive
                        ? "border-primary shadow-sm"
                        : "border-gray-200"
                    } cursor-pointer hover:border-primary/50 transition-colors`}
                  >
                    <img
                      src={page.image}
                      alt={page.title}
                      className="w-full h-20 object-cover rounded mb-2"
                    />
                    <p className="text-sm text-gray-600">{page.title}</p>
                  </div>
                ))}
              </div>
              <Button className="w-full mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Add Page
              </Button>
            </div>
          </div>

          {/* Center - Main Editor */}
          <div className="lg:col-span-2">
            <div className="bg-white border border-gray-200 rounded-lg p-8 min-h-96">
              <div className="text-center mb-6">
                <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
                  Page Editor
                </h3>
                <p className="text-sm text-gray-600">
                  Click to edit text, drag to rearrange elements
                </p>
              </div>

              {/* Mock book page layout */}
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8 aspect-[4/3] relative">
                <img
                  src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600"
                  alt="Fairy tale forest"
                  className="w-full h-32 object-cover rounded-lg mb-4"
                />

                <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4">
                  <h4 className="font-poppins text-lg font-semibold text-gray-900 mb-2">
                    Chapter 1: The Magic Forest
                  </h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    Once upon a time, in a magical forest where the trees
                    whispered secrets and the flowers danced in the wind, there
                    lived a curious little rabbit named Luna...
                  </p>
                </div>

                {/* Edit indicator */}
                <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded text-xs">
                  <Edit className="inline h-3 w-3 mr-1" />
                  Edit
                </div>
              </div>

              {/* Toolbar */}
              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-4">
                  <Button variant="outline" size="sm">
                    <Type className="mr-2 h-4 w-4" />
                    Text
                  </Button>
                  <Button variant="outline" size="sm">
                    <Image className="mr-2 h-4 w-4" />
                    Image
                  </Button>
                  <Button variant="outline" size="sm">
                    <Shapes className="mr-2 h-4 w-4" />
                    Shapes
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
