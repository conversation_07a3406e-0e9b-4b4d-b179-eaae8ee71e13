-- Initialize StoryWizard Database
-- This script runs automatically when PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Note: Tables will be created by Drizzle migrations
-- This file is for any initial setup that needs to happen before the app starts

-- Set timezone
SET timezone = 'UTC';

-- Create a basic health check function
CREATE OR REPLACE FUNCTION health_check()
RETURNS TEXT AS $$
BEGIN
    RETURN 'PostgreSQL is ready for StoryWizard!';
END;
$$ LANGUAGE plpgsql;