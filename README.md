# StoryWizard 📚✨

A full-stack web application for creating and editing story books with cloud storage, user authentication, and subscription management.

## 🚀 Features

- **Story Book Creation**: Create and edit interactive story books with multiple pages
- **Cloud Storage**: Azure Blob Storage integration for scalable file management
- **User Authentication**: JWT-based authentication with secure user management
- **Subscription Tiers**: Free, Basic, and Pro tiers with usage limits
- **Payment Integration**: Stripe integration for subscription management
- **PDF Export**: Generate downloadable PDF versions of story books
- **Responsive Design**: Modern UI with Tailwind CSS and Radix UI components

## 🛠️ Tech Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** for accessible components
- **Wouter** for lightweight routing
- **React Query** for server state management

### Backend

- **Express.js** with TypeScript
- **Node.js** runtime
- **PostgreSQL** with Drizzle ORM
- **Azure Blob Storage** for file storage
- **JWT** authentication
- **Stripe** for payments
- **PDFKit** for PDF generation

### Development Tools

- **ESLint** for code linting
- **Prettier** for code formatting
- **Vitest** for testing
- **Husky** for Git hooks
- **TypeScript** for type safety

## 📋 Prerequisites

- **Node.js** 18.0.0 or higher
- **npm** 8.0.0 or higher
- **PostgreSQL** database
- **Azure Storage Account**
- **Stripe Account** (optional, for payments)

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/storywizard.git
cd storywizard
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Copy the example environment file and configure your variables:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/storywizard

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production

# Azure Storage Configuration
AZURE_STORAGE_ACCOUNT_NAME=your-storage-account-name
AZURE_STORAGE_ACCOUNT_KEY=your-storage-account-key
AZURE_STORAGE_CONTAINER_NAME=storywizard-files

# Stripe Configuration (Optional)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 4. Database Setup

Push the database schema:

```bash
npm run db:push
```

### 5. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## 📝 Available Scripts

### Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server

### Code Quality

- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### Testing

- `npm run test` - Run tests in watch mode
- `npm run test:run` - Run tests once
- `npm run test:coverage` - Run tests with coverage
- `npm run test:ui` - Open Vitest UI

### Database

- `npm run db:push` - Push schema changes
- `npm run db:generate` - Generate migrations
- `npm run db:migrate` - Run migrations

### Validation

- `npm run validate` - Run all quality checks
- `npm run check` - TypeScript type checking

## 🏗️ Project Structure

```
storywizard/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── lib/           # Utilities and configurations
│   │   └── hooks/         # Custom React hooks
├── server/                # Express backend
│   ├── routes.ts          # API routes
│   ├── storage.ts         # Database layer
│   ├── azure-storage.ts   # Azure Blob Storage service
│   └── vite.ts           # Development server setup
├── shared/               # Shared code
│   └── schema.ts         # Database schema and validation
├── src/test/            # Test setup and utilities
└── .vscode/             # VSCode configuration
```

## 🧪 Testing

The project uses Vitest for testing with React Testing Library for component testing.

### Running Tests

```bash
# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Run with coverage
npm run test:coverage

# Open test UI
npm run test:ui
```

### Writing Tests

Test files should be placed in `__tests__` directories or use the `.test.ts/.test.tsx` suffix.

Example:

```typescript
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Button } from '@/components/ui/button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

## 🔒 Authentication

The application uses JWT tokens for authentication:

- Tokens are stored in localStorage
- Automatic token injection via fetch interceptor
- Protected routes require valid authentication
- Token expiration handling with automatic logout

## 💳 Subscription Tiers

- **Free**: 1 book limit
- **Basic**: 5 books limit
- **Pro**: Unlimited books

Subscription management is handled through Stripe webhooks for real-time updates.

## ☁️ Cloud Storage

Files are stored in Azure Blob Storage with the following organization:

- Container: `storywizard-files`
- Structure: `users/{userId}/{timestamp}-{filename}`
- No local file dependencies

## 🚀 Deployment

### Production Build

```bash
npm run build
```

### Environment Variables

Ensure all required environment variables are set in your production environment:

- Database connection string
- Azure Storage credentials
- JWT secret
- Stripe keys (if using payments)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style (enforced by ESLint and Prettier)
- Write tests for new features
- Update documentation as needed
- Ensure all quality checks pass (`npm run validate`)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [React](https://reactjs.org/) for the frontend framework
- [Express.js](https://expressjs.com/) for the backend framework
- [Radix UI](https://www.radix-ui.com/) for accessible components
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Drizzle ORM](https://orm.drizzle.team/) for database management
- [Azure Blob Storage](https://azure.microsoft.com/en-us/services/storage/blobs/) for cloud storage
- [Stripe](https://stripe.com/) for payment processing
