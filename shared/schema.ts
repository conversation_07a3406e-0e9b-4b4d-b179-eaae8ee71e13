import {
  pgTable,
  text,
  serial,
  integer,
  timestamp,
  jsonb,
  boolean,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  name: text("name").notNull(),
  subscriptionTier: text("subscription_tier").notNull().default("free"), // free, basic, pro
  booksUsed: integer("books_used").notNull().default(0),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  role: text("role").notNull().default("user"), // user, admin, moderator
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const books = pgTable("books", {
  id: serial("id").primaryKey(),
  userId: integer("user_id")
    .references(() => users.id)
    .notNull(),
  title: text("title").notNull(),
  description: text("description"),
  coverImage: text("cover_image"),
  pages: jsonb("pages").notNull().default("[]"), // Array of page objects
  status: text("status").notNull().default("draft"), // draft, published
  moderationStatus: text("moderation_status").notNull().default("pending"), // pending, approved, rejected, flagged
  moderationNotes: text("moderation_notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const bookPages = pgTable("book_pages", {
  id: serial("id").primaryKey(),
  bookId: integer("book_id")
    .references(() => books.id)
    .notNull(),
  pageNumber: integer("page_number").notNull(),
  imageUrl: text("image_url"),
  text: text("text"),
  layout: jsonb("layout").notNull().default("{}"), // Layout configuration
  moderationStatus: text("moderation_status").notNull().default("pending"), // pending, approved, rejected, flagged
  moderationNotes: text("moderation_notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const userFiles = pgTable("user_files", {
  id: serial("id").primaryKey(),
  userId: integer("user_id")
    .references(() => users.id)
    .notNull(),
  filename: text("filename").notNull(),
  originalName: text("original_name").notNull(),
  mimetype: text("mimetype").notNull(),
  size: integer("size").notNull(),
  path: text("path").notNull(),
  moderationStatus: text("moderation_status").notNull().default("pending"), // pending, approved, rejected, flagged
  moderationNotes: text("moderation_notes"),
  moderationScore: jsonb("moderation_score"), // Stores detailed moderation results
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const contentModerationLogs = pgTable("content_moderation_logs", {
  id: serial("id").primaryKey(),
  contentType: text("content_type").notNull(), // book, page, file
  contentId: integer("content_id").notNull(),
  userId: integer("user_id")
    .references(() => users.id)
    .notNull(),
  moderatorId: integer("moderator_id").references(() => users.id),
  action: text("action").notNull(), // auto_approve, auto_reject, manual_review, appeal
  previousStatus: text("previous_status"),
  newStatus: text("new_status").notNull(),
  moderationScore: jsonb("moderation_score"), // Detailed API results
  reason: text("reason"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  email: true,
  password: true,
  name: true,
});

export const insertBookSchema = createInsertSchema(books).pick({
  title: true,
  description: true,
  coverImage: true,
});

export const insertBookPageSchema = createInsertSchema(bookPages).pick({
  bookId: true,
  pageNumber: true,
  imageUrl: true,
  text: true,
  layout: true,
});

export const insertUserFileSchema = createInsertSchema(userFiles).pick({
  filename: true,
  originalName: true,
  mimetype: true,
  size: true,
  path: true,
});

export const insertContentModerationLogSchema = createInsertSchema(contentModerationLogs).pick({
  contentType: true,
  contentId: true,
  userId: true,
  moderatorId: true,
  action: true,
  previousStatus: true,
  newStatus: true,
  moderationScore: true,
  reason: true,
  notes: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertBook = z.infer<typeof insertBookSchema>;
export type Book = typeof books.$inferSelect;
export type InsertBookPage = z.infer<typeof insertBookPageSchema>;
export type BookPage = typeof bookPages.$inferSelect;
export type InsertUserFile = z.infer<typeof insertUserFileSchema>;
export type UserFile = typeof userFiles.$inferSelect;
export type InsertContentModerationLog = z.infer<typeof insertContentModerationLogSchema>;
export type ContentModerationLog = typeof contentModerationLogs.$inferSelect;

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const signupSchema = insertUserSchema.extend({
  password: z.string().min(6),
});

export const moderationStatusSchema = z.enum(["pending", "approved", "rejected", "flagged"]);
export const userRoleSchema = z.enum(["user", "admin", "moderator"]);
export const contentTypeSchema = z.enum(["book", "page", "file"]);
export const moderationActionSchema = z.enum(["auto_approve", "auto_reject", "manual_review", "appeal", "admin_override"]);

export type LoginData = z.infer<typeof loginSchema>;
export type SignupData = z.infer<typeof signupSchema>;
export type ModerationStatus = z.infer<typeof moderationStatusSchema>;
export type UserRole = z.infer<typeof userRoleSchema>;
export type ContentType = z.infer<typeof contentTypeSchema>;
export type ModerationAction = z.infer<typeof moderationActionSchema>;
