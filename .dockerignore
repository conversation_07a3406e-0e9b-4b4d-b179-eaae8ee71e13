# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
# dist - needed for production stage
build
*.tsbuildinfo

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# Testing
coverage/
.nyc_output
.coverage
.cache

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Claude Code
.claude/

# Development tools
.husky/
.prettierrc
.prettierignore
eslint.config.js
vitest.config.ts

# Test files
**/*.test.*
**/*.spec.*
**/__tests__/

# Temporary files
tmp/
temp/