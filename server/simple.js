const express = require("express");
const path = require("path");

const app = express();
const PORT = process.env.PORT || 3001;

// Serve static files from the dist/public directory
app.use(express.static(path.join(__dirname, "..", "dist", "public")));

// Serve the React app for all routes
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "..", "dist", "public", "index.html"));
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(
    `📁 Serving files from: ${path.join(__dirname, "..", "dist", "public")}`
  );
});
