import { 
  contentModerationService, 
  TextModerationResult, 
  ImageModerationResult 
} from './content-moderation';
import { 
  contentModerationLogs, 
  books, 
  bookPages, 
  userFiles, 
  type ModerationStatus,
  type ContentType,
  type ModerationAction
} from '../shared/schema';
import { eq } from 'drizzle-orm';

// Import database connection
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const db = drizzle(pool);

export class ModerationService {
  
  /**
   * Moderate text content and update database record
   */
  async moderateTextContent(
    contentType: ContentType,
    contentId: number,
    userId: number,
    text: string
  ): Promise<{ 
    status: ModerationStatus; 
    result: TextModerationResult; 
    action: ModerationAction 
  }> {
    try {
      // Get moderation result from content moderation service
      const result = await contentModerationService.moderateText(text);
      
      // Determine action based on result
      const action = contentModerationService.getAutoModerationAction(result.score, result.isAppropriate);
      
      // Map action to moderation status
      const status: ModerationStatus = this.getStatusFromAction(action);
      
      // Update the content record with moderation status
      await this.updateContentStatus(contentType, contentId, status, result.reason);
      
      // Log the moderation action
      await this.logModerationAction({
        contentType,
        contentId,
        userId,
        action,
        previousStatus: 'pending',
        newStatus: status,
        moderationScore: {
          score: result.score,
          categories: result.categories,
          flaggedTerms: result.flaggedTerms,
          type: 'text'
        },
        reason: result.reason,
        notes: `Auto-moderated text content. Score: ${result.score}`
      });
      
      return { status, result, action };
      
    } catch (error) {
      console.error('Text moderation error:', error);
      
      // On error, flag for manual review
      const status: ModerationStatus = 'flagged';
      await this.updateContentStatus(contentType, contentId, status, 'Moderation service error - requires manual review');
      
      await this.logModerationAction({
        contentType,
        contentId,
        userId,
        action: 'manual_review',
        previousStatus: 'pending',
        newStatus: status,
        reason: 'Moderation service error',
        notes: `Error during text moderation: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      
      return { 
        status, 
        result: {
          isAppropriate: false,
          score: 1.0,
          categories: { hate: 0, selfHarm: 0, sexual: 0, violence: 0 },
          flaggedTerms: [],
          reason: 'Moderation service error'
        },
        action: 'manual_review'
      };
    }
  }

  /**
   * Moderate image content and update database record
   */
  async moderateImageContent(
    contentType: ContentType,
    contentId: number,
    userId: number,
    imageBuffer: Buffer
  ): Promise<{ 
    status: ModerationStatus; 
    result: ImageModerationResult; 
    action: ModerationAction 
  }> {
    try {
      // Get moderation result from content moderation service
      const result = await contentModerationService.moderateImage(imageBuffer);
      
      // Determine action based on result
      const action = contentModerationService.getAutoModerationAction(result.score, result.isAppropriate);
      
      // Map action to moderation status
      const status: ModerationStatus = this.getStatusFromAction(action);
      
      // Update the content record with moderation status
      await this.updateContentStatus(contentType, contentId, status, result.reason);
      
      // Log the moderation action
      await this.logModerationAction({
        contentType,
        contentId,
        userId,
        action,
        previousStatus: 'pending',
        newStatus: status,
        moderationScore: {
          score: result.score,
          categories: result.categories,
          type: 'image'
        },
        reason: result.reason,
        notes: `Auto-moderated image content. Score: ${result.score}`
      });
      
      return { status, result, action };
      
    } catch (error) {
      console.error('Image moderation error:', error);
      
      // On error, flag for manual review
      const status: ModerationStatus = 'flagged';
      await this.updateContentStatus(contentType, contentId, status, 'Moderation service error - requires manual review');
      
      await this.logModerationAction({
        contentType,
        contentId,
        userId,
        action: 'manual_review',
        previousStatus: 'pending',
        newStatus: status,
        reason: 'Moderation service error',
        notes: `Error during image moderation: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      
      return { 
        status, 
        result: {
          isAppropriate: false,
          score: 1.0,
          categories: { adult: 0, racy: 0, violence: 0, gore: 0 },
          reason: 'Moderation service error'
        },
        action: 'manual_review'
      };
    }
  }

  /**
   * Manually approve content (admin/moderator action)
   */
  async approveContent(
    contentType: ContentType,
    contentId: number,
    moderatorId: number,
    notes?: string
  ): Promise<void> {
    const previousStatus = await this.getContentStatus(contentType, contentId);
    
    await this.updateContentStatus(contentType, contentId, 'approved', notes);
    
    await this.logModerationAction({
      contentType,
      contentId,
      userId: await this.getContentUserId(contentType, contentId),
      moderatorId,
      action: 'admin_override',
      previousStatus,
      newStatus: 'approved',
      reason: 'Manual approval by moderator',
      notes
    });
  }

  /**
   * Manually reject content (admin/moderator action)
   */
  async rejectContent(
    contentType: ContentType,
    contentId: number,
    moderatorId: number,
    reason: string,
    notes?: string
  ): Promise<void> {
    const previousStatus = await this.getContentStatus(contentType, contentId);
    
    await this.updateContentStatus(contentType, contentId, 'rejected', reason);
    
    await this.logModerationAction({
      contentType,
      contentId,
      userId: await this.getContentUserId(contentType, contentId),
      moderatorId,
      action: 'admin_override',
      previousStatus,
      newStatus: 'rejected',
      reason,
      notes
    });
  }

  /**
   * Get content that needs manual review
   */
  async getContentForReview(limit: number = 50): Promise<Array<{
    id: number;
    type: ContentType;
    userId: number;
    status: ModerationStatus;
    content: any;
    createdAt: Date;
  }>> {
    // Get flagged books
    const flaggedBooks = await db.select({
      id: books.id,
      type: 'book' as ContentType,
      userId: books.userId,
      status: books.moderationStatus,
      content: {
        title: books.title,
        description: books.description,
        coverImage: books.coverImage
      },
      createdAt: books.createdAt
    })
    .from(books)
    .where(eq(books.moderationStatus, 'flagged'))
    .limit(limit);

    // Get flagged pages
    const flaggedPages = await db.select({
      id: bookPages.id,
      type: 'page' as ContentType,
      userId: books.userId,
      status: bookPages.moderationStatus,
      content: {
        text: bookPages.text,
        imageUrl: bookPages.imageUrl,
        bookId: bookPages.bookId,
        pageNumber: bookPages.pageNumber
      },
      createdAt: bookPages.createdAt
    })
    .from(bookPages)
    .leftJoin(books, eq(bookPages.bookId, books.id))
    .where(eq(bookPages.moderationStatus, 'flagged'))
    .limit(limit);

    // Get flagged files
    const flaggedFiles = await db.select({
      id: userFiles.id,
      type: 'file' as ContentType,
      userId: userFiles.userId,
      status: userFiles.moderationStatus,
      content: {
        filename: userFiles.filename,
        originalName: userFiles.originalName,
        path: userFiles.path,
        mimetype: userFiles.mimetype
      },
      createdAt: userFiles.createdAt
    })
    .from(userFiles)
    .where(eq(userFiles.moderationStatus, 'flagged'))
    .limit(limit);

    // Combine and sort by creation date
    return [...flaggedBooks, ...flaggedPages, ...flaggedFiles]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  /**
   * Get moderation statistics
   */
  async getModerationStats(): Promise<{
    pending: number;
    approved: number;
    rejected: number;
    flagged: number;
    totalReviewed: number;
  }> {
    const [bookStats] = await db.select({
      pending: books.moderationStatus,
      approved: books.moderationStatus,
      rejected: books.moderationStatus,
      flagged: books.moderationStatus,
    }).from(books);

    // This is a simplified version - in a real implementation you'd want proper aggregate queries
    return {
      pending: 0,
      approved: 0,
      rejected: 0,
      flagged: 0,
      totalReviewed: 0
    };
  }

  /**
   * Helper: Map moderation action to status
   */
  private getStatusFromAction(action: ModerationAction): ModerationStatus {
    switch (action) {
      case 'auto_approve':
        return 'approved';
      case 'auto_reject':
        return 'rejected';
      case 'manual_review':
        return 'flagged';
      case 'admin_override':
      case 'appeal':
        return 'pending';
      default:
        return 'pending';
    }
  }

  /**
   * Helper: Update content moderation status
   */
  private async updateContentStatus(
    contentType: ContentType,
    contentId: number,
    status: ModerationStatus,
    notes?: string
  ): Promise<void> {
    switch (contentType) {
      case 'book':
        await db.update(books)
          .set({ 
            moderationStatus: status, 
            moderationNotes: notes,
            updatedAt: new Date()
          })
          .where(eq(books.id, contentId));
        break;
      
      case 'page':
        await db.update(bookPages)
          .set({ 
            moderationStatus: status, 
            moderationNotes: notes 
          })
          .where(eq(bookPages.id, contentId));
        break;
      
      case 'file':
        await db.update(userFiles)
          .set({ 
            moderationStatus: status, 
            moderationNotes: notes 
          })
          .where(eq(userFiles.id, contentId));
        break;
    }
  }

  /**
   * Helper: Get current content status
   */
  private async getContentStatus(contentType: ContentType, contentId: number): Promise<ModerationStatus> {
    switch (contentType) {
      case 'book':
        const book = await db.select({ status: books.moderationStatus })
          .from(books)
          .where(eq(books.id, contentId))
          .limit(1);
        return (book[0]?.status as ModerationStatus) || 'pending';
      
      case 'page':
        const page = await db.select({ status: bookPages.moderationStatus })
          .from(bookPages)
          .where(eq(bookPages.id, contentId))
          .limit(1);
        return (page[0]?.status as ModerationStatus) || 'pending';
      
      case 'file':
        const file = await db.select({ status: userFiles.moderationStatus })
          .from(userFiles)
          .where(eq(userFiles.id, contentId))
          .limit(1);
        return (file[0]?.status as ModerationStatus) || 'pending';
      
      default:
        return 'pending';
    }
  }

  /**
   * Helper: Get content owner user ID
   */
  private async getContentUserId(contentType: ContentType, contentId: number): Promise<number> {
    switch (contentType) {
      case 'book':
        const book = await db.select({ userId: books.userId })
          .from(books)
          .where(eq(books.id, contentId))
          .limit(1);
        return book[0]?.userId || 0;
      
      case 'page':
        const page = await db.select({ userId: books.userId })
          .from(bookPages)
          .leftJoin(books, eq(bookPages.bookId, books.id))
          .where(eq(bookPages.id, contentId))
          .limit(1);
        return page[0]?.userId || 0;
      
      case 'file':
        const file = await db.select({ userId: userFiles.userId })
          .from(userFiles)
          .where(eq(userFiles.id, contentId))
          .limit(1);
        return file[0]?.userId || 0;
      
      default:
        return 0;
    }
  }

  /**
   * Helper: Log moderation action
   */
  private async logModerationAction(logData: {
    contentType: ContentType;
    contentId: number;
    userId: number;
    moderatorId?: number;
    action: ModerationAction;
    previousStatus?: string;
    newStatus: ModerationStatus;
    moderationScore?: any;
    reason?: string;
    notes?: string;
  }): Promise<void> {
    await db.insert(contentModerationLogs).values({
      contentType: logData.contentType,
      contentId: logData.contentId,
      userId: logData.userId,
      moderatorId: logData.moderatorId || null,
      action: logData.action,
      previousStatus: logData.previousStatus || null,
      newStatus: logData.newStatus,
      moderationScore: logData.moderationScore ? JSON.parse(JSON.stringify(logData.moderationScore)) : null,
      reason: logData.reason || null,
      notes: logData.notes || null,
    });
  }
}

// Export singleton instance
export const moderationService = new ModerationService();