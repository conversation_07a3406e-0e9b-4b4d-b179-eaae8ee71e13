# Docker Container Fixes for StoryWizard

## Issues Diagnosed

The Docker container is failing to start properly due to several configuration issues:

1. **Complex shell parsing in Dockerfile CMD** - The current inline script is fragile
2. **Unused startup script** - There's a proper `scripts/docker-start.sh` but it's not being used
3. **Build context conflicts** - `.dockerignore` excludes `dist` but Dockerfile needs it
4. **Environment mismatch** - docker-compose sets `NODE_ENV=development` but built app needs `production`
5. **Missing dependencies** - Production stage might be missing `drizzle-kit` for migrations

## Fixes Required

### 1. Update Dockerfile to use startup script

Replace the complex CMD with:

```dockerfile
# Copy the startup script and make it executable
COPY --chown=storywizard:nodejs scripts/docker-start.sh ./scripts/
RUN chmod +x ./scripts/docker-start.sh

# Ensure drizzle-kit is available for migrations
RUN npm ci --omit=dev && npm install drizzle-kit && npm cache clean --force

# Use the startup script
CMD ["./scripts/docker-start.sh"]
```

### 2. Fix .dockerignore

Remove `dist` from `.dockerignore` since we need it from builder stage:

```dockerignore
# Remove this line:
# dist

# Keep these:
build
*.tsbuildinfo
```

### 3. Update docker-compose.yml environment

Change NODE_ENV to production:

```yaml
environment:
  NODE_ENV: production # Changed from development
```

### 4. Update scripts/docker-start.sh

Ensure the startup script works properly in container:

```bash
#!/bin/bash

# Docker startup script for StoryWizard
set -e

echo "🚀 Starting StoryWizard Docker container..."

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
while ! pg_isready -h postgres -p 5432 -U storywizard; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Run database migrations
echo "📊 Running database migrations..."
npx drizzle-kit push --force

echo "🌟 Starting StoryWizard application..."
exec npm start
```

## Testing Steps

After applying these fixes:

1. **Build and test locally:**

   ```bash
   docker-compose down
   docker-compose build --no-cache
   docker-compose up
   ```

2. **Check logs:**

   ```bash
   docker-compose logs -f app
   ```

3. **Verify health:**
   ```bash
   curl http://localhost:5000/api/health
   ```

## Root Cause

The main issue is that the Dockerfile was trying to do too much in the CMD instruction with complex shell parsing, while a proper startup script already existed but wasn't being used. The environment configuration mismatch (development vs production) was also causing issues with the built application.

## Implementation Priority

1. **High Priority:** Update Dockerfile to use startup script
2. **High Priority:** Fix NODE_ENV in docker-compose.yml
3. **Medium Priority:** Update .dockerignore
4. **Low Priority:** Verify startup script permissions

These changes should resolve the Docker container startup issues and make the deployment more reliable.
