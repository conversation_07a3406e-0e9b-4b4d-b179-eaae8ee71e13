import {
  type User,
  type InsertUser,
  type Book,
  type InsertBook,
  type BookPage,
  type InsertBookPage,
  type UserFile,
  type InsertUserFile,
} from "../shared/schema";
import * as bcrypt from "bcrypt";

// PostgreSQL imports (only used if DATABASE_URL is available)
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { 
  users, 
  books, 
  bookPages, 
  userFiles
} from "../shared/schema";
import { eq } from 'drizzle-orm';

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  updateUserStripeInfo(
    id: number,
    stripeCustomerId: string,
    stripeSubscriptionId: string
  ): Promise<User | undefined>;

  // Book management
  getBooksByUserId(userId: number): Promise<Book[]>;
  getBook(id: number): Promise<Book | undefined>;
  createBook(book: InsertBook & { userId: number }): Promise<Book>;
  updateBook(id: number, updates: Partial<Book>): Promise<Book | undefined>;
  deleteBook(id: number): Promise<boolean>;

  // Book pages
  getBookPages(bookId: number): Promise<BookPage[]>;
  createBookPage(page: InsertBookPage & { userId: number }): Promise<BookPage>;
  updateBookPage(
    id: number,
    updates: Partial<BookPage>
  ): Promise<BookPage | undefined>;
  deleteBookPage(id: number): Promise<boolean>;

  // File management
  getUserFiles(userId: number): Promise<UserFile[]>;
  createUserFile(file: InsertUserFile & { userId: number }): Promise<UserFile>;
  getUserFile(id: number): Promise<UserFile | undefined>;
  deleteUserFile(id: number): Promise<boolean>;

  // Health check
  getHealthCheck(): Promise<{ status: string }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private books: Map<number, Book>;
  private bookPages: Map<number, BookPage>;
  private userFiles: Map<number, UserFile>;
  private currentUserId: number;
  private currentBookId: number;
  private currentPageId: number;
  private currentFileId: number;

  constructor() {
    this.users = new Map();
    this.books = new Map();
    this.bookPages = new Map();
    this.userFiles = new Map();
    this.currentUserId = 1;
    this.currentBookId = 1;
    this.currentPageId = 1;
    this.currentFileId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const hashedPassword = await bcrypt.hash(insertUser.password, 10);
    const user: User = {
      id,
      email: insertUser.email,
      password: hashedPassword,
      name: insertUser.name,
      subscriptionTier: "free",
      booksUsed: 0,
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      role: "user",
      createdAt: new Date(),
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(
    id: number,
    updates: Partial<User>
  ): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;

    const updatedUser = { ...user, ...updates };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async updateUserStripeInfo(
    id: number,
    stripeCustomerId: string,
    stripeSubscriptionId: string
  ): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;

    const updatedUser = { ...user, stripeCustomerId, stripeSubscriptionId };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async getBooksByUserId(userId: number): Promise<Book[]> {
    return Array.from(this.books.values()).filter(
      book => book.userId === userId
    );
  }

  async getBook(id: number): Promise<Book | undefined> {
    return this.books.get(id);
  }

  async createBook(bookData: InsertBook & { userId: number }): Promise<Book> {
    const id = this.currentBookId++;
    const book: Book = {
      id,
      userId: bookData.userId,
      title: bookData.title,
      description: bookData.description || null,
      coverImage: bookData.coverImage || null,
      pages: [],
      status: "draft",
      moderationStatus: "pending",
      moderationNotes: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.books.set(id, book);
    return book;
  }

  async updateBook(
    id: number,
    updates: Partial<Book>
  ): Promise<Book | undefined> {
    const book = this.books.get(id);
    if (!book) return undefined;

    const updatedBook = { ...book, ...updates, updatedAt: new Date() };
    this.books.set(id, updatedBook);
    return updatedBook;
  }

  async deleteBook(id: number): Promise<boolean> {
    // Also delete associated pages
    const pagesToDelete = Array.from(this.bookPages.values()).filter(
      page => page.bookId === id
    );

    pagesToDelete.forEach(page => this.bookPages.delete(page.id));

    return this.books.delete(id);
  }

  async getBookPages(bookId: number): Promise<BookPage[]> {
    return Array.from(this.bookPages.values())
      .filter(page => page.bookId === bookId)
      .sort((a, b) => a.pageNumber - b.pageNumber);
  }

  async createBookPage(
    pageData: InsertBookPage & { userId: number }
  ): Promise<BookPage> {
    const id = this.currentPageId++;
    const page: BookPage = {
      id,
      bookId: pageData.bookId,
      pageNumber: pageData.pageNumber,
      imageUrl: pageData.imageUrl || null,
      text: pageData.text || null,
      layout: pageData.layout || {},
      moderationStatus: "pending",
      moderationNotes: null,
      createdAt: new Date(),
    };
    this.bookPages.set(id, page);
    return page;
  }

  async updateBookPage(
    id: number,
    updates: Partial<BookPage>
  ): Promise<BookPage | undefined> {
    const page = this.bookPages.get(id);
    if (!page) return undefined;

    const updatedPage = { ...page, ...updates };
    this.bookPages.set(id, updatedPage);
    return updatedPage;
  }

  async deleteBookPage(id: number): Promise<boolean> {
    return this.bookPages.delete(id);
  }

  async getUserFiles(userId: number): Promise<UserFile[]> {
    return Array.from(this.userFiles.values()).filter(
      file => file.userId === userId
    );
  }

  async createUserFile(
    fileData: InsertUserFile & { userId: number }
  ): Promise<UserFile> {
    const id = this.currentFileId++;
    const file: UserFile = {
      id,
      userId: fileData.userId,
      filename: fileData.filename,
      originalName: fileData.originalName,
      mimetype: fileData.mimetype,
      size: fileData.size,
      path: fileData.path,
      moderationStatus: "pending",
      moderationNotes: null,
      moderationScore: null,
      createdAt: new Date(),
    };
    this.userFiles.set(id, file);
    return file;
  }

  async getUserFile(id: number): Promise<UserFile | undefined> {
    return this.userFiles.get(id);
  }

  async deleteUserFile(id: number): Promise<boolean> {
    return this.userFiles.delete(id);
  }

  async getHealthCheck(): Promise<{ status: string }> {
    // For in-memory storage, always return healthy
    return { status: "healthy" };
  }
}

// PostgreSQL Storage Implementation
class PostgresStorage implements IStorage {
  private db: any;

  constructor() {
    if (process.env.DATABASE_URL) {
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
      });
      this.db = drizzle(pool);
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const hashedPassword = await bcrypt.hash(user.password, 12);
    
    const result = await this.db.insert(users).values({
      email: user.email,
      password: hashedPassword,
      name: user.name,
    }).returning();
    
    return result[0];
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const result = await this.db.update(users)
      .set({
        ...updates,
        password: updates.password ? await bcrypt.hash(updates.password, 12) : undefined,
      })
      .where(eq(users.id, id))
      .returning();
    
    return result[0];
  }

  async updateUserStripeInfo(
    id: number,
    stripeCustomerId: string,
    stripeSubscriptionId: string
  ): Promise<User | undefined> {
    const result = await this.db.update(users)
      .set({
        stripeCustomerId,
        stripeSubscriptionId,
      })
      .where(eq(users.id, id))
      .returning();
    
    return result[0];
  }

  async getBooksByUserId(userId: number): Promise<Book[]> {
    const result = await this.db.select().from(books).where(eq(books.userId, userId));
    return result;
  }

  async getBook(id: number): Promise<Book | undefined> {
    const result = await this.db.select().from(books).where(eq(books.id, id)).limit(1);
    return result[0];
  }

  async createBook(book: InsertBook & { userId: number }): Promise<Book> {
    const result = await this.db.insert(books).values({
      title: book.title,
      description: book.description,
      coverImage: book.coverImage,
      userId: book.userId,
    }).returning();
    
    return result[0];
  }

  async updateBook(id: number, updates: Partial<Book>): Promise<Book | undefined> {
    const result = await this.db.update(books)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(books.id, id))
      .returning();
    
    return result[0];
  }

  async deleteBook(id: number): Promise<boolean> {
    await this.db.delete(bookPages).where(eq(bookPages.bookId, id));
    const result = await this.db.delete(books).where(eq(books.id, id)).returning();
    return result.length > 0;
  }

  async getBookPages(bookId: number): Promise<BookPage[]> {
    const result = await this.db.select().from(bookPages)
      .where(eq(bookPages.bookId, bookId))
      .orderBy(bookPages.pageNumber);
    
    return result;
  }

  async createBookPage(page: InsertBookPage & { userId: number }): Promise<BookPage> {
    const result = await this.db.insert(bookPages).values({
      bookId: page.bookId,
      pageNumber: page.pageNumber,
      imageUrl: page.imageUrl,
      text: page.text,
      layout: page.layout,
    }).returning();
    
    return result[0];
  }

  async updateBookPage(id: number, updates: Partial<BookPage>): Promise<BookPage | undefined> {
    const result = await this.db.update(bookPages)
      .set(updates)
      .where(eq(bookPages.id, id))
      .returning();
    
    return result[0];
  }

  async deleteBookPage(id: number): Promise<boolean> {
    const result = await this.db.delete(bookPages).where(eq(bookPages.id, id)).returning();
    return result.length > 0;
  }

  async getUserFiles(userId: number): Promise<UserFile[]> {
    const result = await this.db.select().from(userFiles).where(eq(userFiles.userId, userId));
    return result;
  }

  async createUserFile(file: InsertUserFile & { userId: number }): Promise<UserFile> {
    const result = await this.db.insert(userFiles).values({
      userId: file.userId,
      filename: file.filename,
      originalName: file.originalName,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
    }).returning();
    
    return result[0];
  }

  async getUserFile(id: number): Promise<UserFile | undefined> {
    const result = await this.db.select().from(userFiles).where(eq(userFiles.id, id)).limit(1);
    return result[0];
  }

  async deleteUserFile(id: number): Promise<boolean> {
    const result = await this.db.delete(userFiles).where(eq(userFiles.id, id)).returning();
    return result.length > 0;
  }

  async getHealthCheck(): Promise<{ status: string }> {
    try {
      await this.db.select().from(users).limit(1);
      return { status: "healthy" };
    } catch (error) {
      console.error("Database health check failed:", error);
      throw new Error("Database connection failed");
    }
  }
}

// Export storage instance based on environment
export const storage = process.env.DATABASE_URL 
  ? new PostgresStorage()
  : new MemStorage();
