{"permissions": {"allow": ["Bash(npm install:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(npx husky:*)", "Bash(ls:*)", "Bash(npm run format:check:*)", "Bash(npm run format:*)", "Bash(npm run check)", "Bash(npm run test:run:*)", "Bash(npm run validate:*)", "Bash(npm run dev:*)", "Bash(timeout 5s npm run dev:*)", "Bash(node:*)", "<PERSON><PERSON>(tsx:*)", "Bash(npx tsx:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(curl:*)", "Bash(NODE_ENV=development npx tsx -e \"\nconst storageConfig = require(''./server/azure-storage.js'');\nconsole.log(''Storage config loaded successfully'');\n\")", "Bash(NODE_ENV=development npx tsx -e \"\nimport(''./server/azure-storage.js'').then(() => {\n  console.log(''✅ Azure Storage service loaded successfully with real credentials'');\n}).catch(e => {\n  console.log(''❌ Error:'', e.message);\n});\n\")", "Bash(NODE_ENV=development npx tsx -e \"\nconsole.log(''Environment variables:'');\nconsole.log(''AZURE_STORAGE_ACCOUNT_NAME_DEV:'', process.env.AZURE_STORAGE_ACCOUNT_NAME_DEV);\nconsole.log(''AZURE_STORAGE_ACCOUNT_KEY_DEV:'', process.env.AZURE_STORAGE_ACCOUNT_KEY_DEV ? ''SET'' : ''NOT SET'');\nconsole.log(''NODE_ENV:'', process.env.NODE_ENV);\n\")", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker-compose config:*)"], "deny": []}}