import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Download, Calendar, Layers } from "lucide-react";
import { Link } from "wouter";
import { Book } from "@shared/schema";

interface BookCardProps {
  book: Book;
}

export function BookCard({ book }: BookCardProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date));
  };

  return (
    <Card className="book-shadow overflow-hidden hover:shadow-lg transition-shadow">
      <div className="aspect-[4/3] bg-gradient-to-br from-blue-50 to-purple-50 relative">
        {book.coverImage ? (
          <img
            src={book.coverImage}
            alt={book.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center">
              <Layers className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No cover image</p>
            </div>
          </div>
        )}

        <div className="absolute top-2 right-2">
          <Badge
            variant={book.status === "published" ? "default" : "secondary"}
            className="text-xs"
          >
            {book.status}
          </Badge>
        </div>
      </div>

      <CardContent className="p-6">
        <h3 className="font-poppins text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {book.title}
        </h3>

        {book.description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {book.description}
          </p>
        )}

        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            <span>Created {formatDate(book.createdAt)}</span>
          </div>
          <div className="flex items-center">
            <Layers className="h-4 w-4 mr-1" />
            <span>
              {Array.isArray(book.pages) ? book.pages.length : 0} pages
            </span>
          </div>
        </div>

        <div className="flex space-x-2">
          <Link href={`/book/${book.id}`}>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </Link>
          <Button size="sm" variant="outline" className="flex-1">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
