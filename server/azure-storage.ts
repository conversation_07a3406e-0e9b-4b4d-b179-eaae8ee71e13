import { config } from "dotenv";
config(); // Ensure environment variables are loaded

import {
  BlobServiceClient,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";
import { Readable } from "stream";

const NODE_ENV = process.env.NODE_ENV || "development";

// Environment-specific storage account configuration
const getStorageConfig = () => {
  if (NODE_ENV === "production") {
    return {
      accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME_PROD || "",
      accountKey: process.env.AZURE_STORAGE_ACCOUNT_KEY_PROD || "",
      containerName:
        process.env.AZURE_STORAGE_CONTAINER_NAME_PROD ||
        "storywizard-prod-files",
      prefix: "", // No prefix for production
    };
  } else {
    // Development and test use the same storage account but different prefixes
    return {
      accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME_DEV || "",
      accountKey: process.env.AZURE_STORAGE_ACCOUNT_KEY_DEV || "",
      containerName:
        process.env.AZURE_STORAGE_CONTAINER_NAME_DEV || "storywizard-dev-files",
      prefix: NODE_ENV === "test" ? "test/" : "dev/",
    };
  }
};

const storageConfig = getStorageConfig();

// Check if we have real Azure Storage credentials (not placeholder values)
const hasRealCredentials =
  storageConfig.accountName &&
  storageConfig.accountKey &&
  storageConfig.accountName !== "your-storage-account-name-dev" &&
  storageConfig.accountName !== "your-storage-account-name-prod" &&
  storageConfig.accountKey !== "your-storage-account-key-dev" &&
  storageConfig.accountKey !== "your-storage-account-key-prod";

if (!hasRealCredentials) {
  if (NODE_ENV === "development") {
    console.warn(
      "⚠️  Azure Storage credentials not provided or using placeholder values."
    );
    console.warn(
      "⚠️  File uploads will fail until real Azure Storage credentials are configured."
    );
    console.warn(
      `⚠️  To fix for ${NODE_ENV}: Update AZURE_STORAGE_ACCOUNT_NAME_DEV and AZURE_STORAGE_ACCOUNT_KEY_DEV in .env`
    );
  } else {
    const envSuffix = NODE_ENV === "production" ? "_PROD" : "_DEV";
    throw new Error(
      `Azure Storage credentials not provided. Set AZURE_STORAGE_ACCOUNT_NAME${envSuffix} and AZURE_STORAGE_ACCOUNT_KEY${envSuffix} environment variables.`
    );
  }
}

// Only create Azure Storage clients if we have real credentials
let sharedKeyCredential: StorageSharedKeyCredential | null = null;
let blobServiceClient: BlobServiceClient | null = null;
let containerClient: any = null;

if (hasRealCredentials) {
  // Create a credential
  sharedKeyCredential = new StorageSharedKeyCredential(
    storageConfig.accountName,
    storageConfig.accountKey
  );

  // Create BlobServiceClient
  blobServiceClient = new BlobServiceClient(
    `https://${storageConfig.accountName}.blob.core.windows.net`,
    sharedKeyCredential
  );

  // Get container client
  containerClient = blobServiceClient.getContainerClient(
    storageConfig.containerName
  );
}

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
}

export class AzureStorageService {
  static async ensureContainer(): Promise<void> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials."
      );
    }
    try {
      await containerClient.createIfNotExists({
        access: "blob", // Allow public read access to blobs
      });
    } catch (error) {
      console.error("Error creating container:", error);
      throw error;
    }
  }

  static async uploadFile(
    buffer: Buffer,
    filename: string,
    mimetype: string,
    userId: number
  ): Promise<UploadResult> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials in .env file."
      );
    }
    try {
      await this.ensureContainer();

      // Create a unique blob name with environment and user folder structure
      const blobName = `${storageConfig.prefix}users/${userId}/${Date.now()}-${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload buffer to blob
      const uploadResponse = await blockBlobClient.upload(
        buffer,
        buffer.length,
        {
          blobHTTPHeaders: {
            blobContentType: mimetype,
          },
        }
      );

      if (!uploadResponse.requestId) {
        throw new Error("Upload failed");
      }

      return {
        url: blockBlobClient.url,
        filename: blobName,
        size: buffer.length,
      };
    } catch (error) {
      console.error("Error uploading file to Azure Blob Storage:", error);
      throw error;
    }
  }

  static async uploadStream(
    stream: Readable,
    filename: string,
    mimetype: string,
    userId: number,
    size: number
  ): Promise<UploadResult> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials in .env file."
      );
    }
    try {
      await this.ensureContainer();

      // Create a unique blob name with environment and user folder structure
      const blobName = `${storageConfig.prefix}users/${userId}/${Date.now()}-${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload stream to blob
      const uploadResponse = await blockBlobClient.uploadStream(
        stream,
        size,
        5,
        {
          blobHTTPHeaders: {
            blobContentType: mimetype,
          },
        }
      );

      if (!uploadResponse.requestId) {
        throw new Error("Upload failed");
      }

      return {
        url: blockBlobClient.url,
        filename: blobName,
        size: size,
      };
    } catch (error) {
      console.error("Error uploading stream to Azure Blob Storage:", error);
      throw error;
    }
  }

  static async deleteFile(filename: string): Promise<void> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials in .env file."
      );
    }
    try {
      const blockBlobClient = containerClient.getBlockBlobClient(filename);
      await blockBlobClient.deleteIfExists();
    } catch (error) {
      console.error("Error deleting file from Azure Blob Storage:", error);
      throw error;
    }
  }

  static async getFileUrl(filename: string): Promise<string> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials in .env file."
      );
    }
    const blockBlobClient = containerClient.getBlockBlobClient(filename);
    return blockBlobClient.url;
  }

  static async fileExists(filename: string): Promise<boolean> {
    if (!containerClient) {
      throw new Error(
        "Azure Storage not configured. Please set up valid credentials in .env file."
      );
    }
    try {
      const blockBlobClient = containerClient.getBlockBlobClient(filename);
      const exists = await blockBlobClient.exists();
      return exists;
    } catch (error) {
      console.error("Error checking file existence:", error);
      return false;
    }
  }
}
