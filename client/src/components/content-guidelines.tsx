import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, Info } from "lucide-react";

export function ContentGuidelines() {
  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Content Guidelines</h1>
        <p className="text-gray-600">
          Our community standards for creating safe, family-friendly story books
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          All user-generated content is automatically scanned and may be reviewed by our moderation team
          to ensure compliance with these guidelines.
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Allowed Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-green-600">
              <CheckCircle className="h-5 w-5 mr-2" />
              Allowed Content
            </CardTitle>
            <CardDescription>
              Content that follows our community standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Educational and entertaining stories for all ages</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Original creative content and artwork</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Positive, inspiring, and uplifting narratives</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Age-appropriate language and themes</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Diverse and inclusive storytelling</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Fantasy, adventure, and imaginative content</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Prohibited Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-red-600">
              <XCircle className="h-5 w-5 mr-2" />
              Prohibited Content
            </CardTitle>
            <CardDescription>
              Content that violates our community standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Adult, sexual, or inappropriate content</span>
              </li>
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Violence, graphic content, or disturbing imagery</span>
              </li>
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Hate speech, discrimination, or offensive language</span>
              </li>
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Copyrighted material without permission</span>
              </li>
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Personal information or privacy violations</span>
              </li>
              <li className="flex items-start">
                <XCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm">Spam, promotional content, or irrelevant material</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Moderation Process */}
      <Card>
        <CardHeader>
          <CardTitle>Content Moderation Process</CardTitle>
          <CardDescription>
            How we review and moderate content on our platform
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-blue-600 font-bold text-sm">1</span>
              </div>
              <h3 className="font-semibold mb-1">Automatic Scanning</h3>
              <p className="text-sm text-gray-600">
                All content is automatically scanned using AI technology
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-yellow-600 font-bold text-sm">2</span>
              </div>
              <h3 className="font-semibold mb-1">Human Review</h3>
              <p className="text-sm text-gray-600">
                Flagged content is reviewed by our moderation team
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-green-600 font-bold text-sm">3</span>
              </div>
              <h3 className="font-semibold mb-1">Approval/Action</h3>
              <p className="text-sm text-gray-600">
                Content is approved, edited, or removed as appropriate
              </p>
            </div>
          </div>
          
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Response Times:</strong> Most content is approved automatically within seconds. 
              Content requiring human review is typically processed within 24 hours.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Appeals Process */}
      <Card>
        <CardHeader>
          <CardTitle>Appeals & Support</CardTitle>
          <CardDescription>
            What to do if your content is flagged or rejected
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <h4 className="font-semibold">If your content is rejected:</h4>
            <ul className="space-y-1 text-sm text-gray-600 ml-4">
              <li>• Review these guidelines and edit your content accordingly</li>
              <li>• Remove any potentially problematic text or images</li>
              <li>• Resubmit your content for review</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-semibold">Need help or think there was a mistake?</h4>
            <p className="text-sm text-gray-600">
              Contact our support team with specific details about your content and we'll review it manually.
              Most appeals are resolved within 48 hours.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Best Practices */}
      <Card>
        <CardHeader>
          <CardTitle>Best Practices for Content Creation</CardTitle>
          <CardDescription>
            Tips for creating content that's engaging and compliant
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-2">For Text Content:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Use clear, age-appropriate language</li>
                <li>• Focus on positive themes and messages</li>
                <li>• Avoid controversial or sensitive topics</li>
                <li>• Include diverse characters and perspectives</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">For Images:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Use original artwork or royalty-free images</li>
                <li>• Ensure images are family-friendly</li>
                <li>• Avoid realistic violence or scary content</li>
                <li>• Use bright, engaging visuals for children</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}